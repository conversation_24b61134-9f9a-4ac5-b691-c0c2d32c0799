DROP SCHEMA IF EXISTS test_resource_repository CASCADE;
CREATE SCHEMA test_resource_repository;

CREATE SEQUENCE test_resource_repository.md_seq START WITH 1;

CREATE TABLE IF NOT EXISTS test_resource_repository.bridge_resource (
    resource_rk bigint NOT NULL DEFAULT nextval('test_resource_repository.md_seq'::regclass),
    resource_cd text NOT NULL,
    resource_desc text,
    is_readonly_flg boolean NOT NULL,
    is_maintenance_flg boolean NOT NULL,
    tag_list text[],
    resource_json jsonb NOT NULL,
    version_rk bigint NOT NULL,
    effective_from_dttm timestamptz NOT NULL,
    effective_to_dttm timestamptz NOT NULL,
    deleted_flg boolean NOT NULL
);

CREATE TABLE IF NOT EXISTS test_resource_repository.bridge_table (
    table_rk bigint NOT NULL DEFAULT nextval('test_resource_repository.md_seq'::regclass),
    table_name text,
    schema_name text,
    effective_from_dttm timestamptz NOT NULL,
    effective_to_dttm timestamptz NOT NULL,
    deleted_flg boolean NOT NULL
);

CREATE TABLE IF NOT EXISTS test_resource_repository.link_resource_table (
    resource_rk bigint NOT NULL,
    table_rk bigint NOT NULL,
    effective_from_dttm timestamptz NOT NULL,
    effective_to_dttm timestamptz NOT NULL,
    deleted_flg boolean NOT NULL
);

CREATE TABLE IF NOT EXISTS test_resource_repository.bridge_source (
    source_rk bigint NOT NULL DEFAULT nextval('test_resource_repository.md_seq'::regclass),
    source_cd text NOT NULL,
    effective_from_dttm timestamptz NOT NULL,
    effective_to_dttm timestamptz NOT NULL,
    deleted_flg boolean NOT NULL
);

CREATE TABLE IF NOT EXISTS test_resource_repository.link_resource_source (
    resource_rk bigint NOT NULL,
    source_rk bigint NOT NULL,
    effective_from_dttm timestamptz NOT NULL,
    effective_to_dttm timestamptz NOT NULL,
    deleted_flg boolean NOT NULL
);

CREATE TABLE IF NOT EXISTS test_resource_repository.bridge_resource_staging (
    resource_cd         TEXT NOT NULL,
    data_action_type_rk SMALLINT NOT NULL,
    resource_type       TEXT,
    definition          JSONB
);
