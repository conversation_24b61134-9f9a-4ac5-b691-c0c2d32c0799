from datetime import datetime

from metaloader_rest_api.models import ResourceAction
from metaloader_rest_api.resource_repository import ResourceRepository

from .conftest import (
    DATE_FROM_MIN,
    DATE_TO_MAX,
    TEST_SCHEMA,
    ResetTablesFunc,
    ResourceRecord,
    SourceLinkRecord,
    SourceRecord,
    StageResourceRecord,
    TableLinkRecord,
    TableReader,
    TableRecord,
)


def test_bridge_resource_historization(
    reset_tables: ResetTablesFunc,
    read_table: TableReader,
    resource_repository: ResourceRepository,
):
    existing_unchanged_1_json = {
        "resource_cd": "exs_unchanged_1",
    }
    existing_unchanged_2_json = {
        "resource_cd": "exs_unchanged_2",
    }
    existing_changed_1_json = {
        "resource_cd": "exs_changed_1",
        "resource_desc": "Foo",
    }
    existing_changed_2_json = {
        "resource_cd": "exs_changed_2",
        "status": {"is_deleted": False},
    }
    existing_deleted_changed_json = {
        "resource_cd": "exs_deleted_1",
    }
    existing_deleted_unchanged_json = {
        "resource_cd": "exs_deleted_2",
    }
    existing_nondeleted_deleted_json = {
        "resource_cd": "exs_nondeleted_deleted",
        "resource_desc": "same",
    }

    reset_tables(
        resources=[
            ResourceRecord(
                resource_rk=1001,
                resource_cd="exs_unchanged_1",
                resource_json=existing_unchanged_1_json,
            ),
            ResourceRecord(
                resource_rk=1002,
                resource_cd="exs_unchanged_2",
                resource_json=existing_unchanged_2_json,
            ),
            ResourceRecord(
                resource_rk=1003,
                resource_cd="exs_changed_1",
                resource_json=existing_changed_1_json,
            ),
            ResourceRecord(
                resource_rk=1004,
                resource_cd="exs_changed_2",
                resource_json=existing_changed_2_json,
            ),
            ResourceRecord(
                resource_rk=1005,
                resource_cd="exs_deleted_unchanged",
                deleted_flg=True,
                resource_json=existing_deleted_unchanged_json,
            ),
            ResourceRecord(
                resource_rk=1006,
                resource_cd="exs_deleted_changed",
                deleted_flg=True,
                resource_json=existing_deleted_changed_json,
            ),
            ResourceRecord(
                resource_rk=1007,
                resource_cd="exs_nondeleted_deleted",
                resource_json=existing_nondeleted_deleted_json,
            ),
        ],
        resources_staging=[
            # Новые ключи
            StageResourceRecord(
                resource_cd="new_1",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
            ),
            StageResourceRecord(
                resource_cd="new_2",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="uni",
            ),
            # Существующие ключи, без изменений содержания
            StageResourceRecord(
                resource_cd="exs_unchanged_1",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
                definition=existing_unchanged_1_json,
            ),
            StageResourceRecord(
                resource_cd="exs_unchanged_2",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="uni",
                definition=existing_unchanged_2_json,
            ),
            # Существующие ключи, с изменением содержания
            StageResourceRecord(
                resource_cd="exs_changed_1",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
                definition={
                    **existing_changed_1_json,
                    "resource_desc": "Bar",
                },
            ),
            StageResourceRecord(
                resource_cd="exs_changed_2",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="uni",
                definition={
                    **existing_changed_2_json,
                    # Проверяем, что историзация реагирует на атрибут,
                    # который есть только в джсоне, но не на уровне таблицы
                    "status": {"is_deleted": True},
                },
            ),
            # Существующие ключи, последняя запись в бридже помечена удалённой
            StageResourceRecord(
                resource_cd="exs_deleted_unchanged",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="ceh",
            ),
            StageResourceRecord(
                resource_cd="exs_deleted_changed",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="uni",
                definition={
                    **existing_deleted_changed_json,
                    "resource_desc": "Bar",
                },
            ),
            # Новый ключ, стал удалённым
            StageResourceRecord(
                resource_cd="new_deleted",
                data_action_type_rk=ResourceAction.DELETE,
                resource_type="ceh",
            ),
            # Существующий ключ, стал удалённым, последняя запись в бридже помечена удалённой
            StageResourceRecord(
                resource_cd="exs_deleted_deleted",
                data_action_type_rk=ResourceAction.DELETE,
                resource_type="uni",
            ),
            # Существующий ключ, стал удалённым
            StageResourceRecord(
                resource_cd="exs_nondeleted_deleted",
                data_action_type_rk=ResourceAction.DELETE,
                resource_type="ceh",
            ),
        ],
    )

    resource_repository.load_resources(
        version_id=1,
        effective_date=datetime(2025, 1, 1),
        stage_table=f"{TEST_SCHEMA}.bridge_resource_staging",
    )
    resource_repository._session.commit()

    bridge_resource = read_table.bridge_resource(
        order_by=["resource_cd", "effective_from_dttm"]
    )
    assert bridge_resource[
        ["resource_cd", "effective_from_dttm", "effective_to_dttm", "deleted_flg"]
    ] == [
        ("exs_changed_1", DATE_FROM_MIN, "2025-01-01", False),
        ("exs_changed_1", "2025-01-01", DATE_TO_MAX, False),
        ("exs_changed_2", DATE_FROM_MIN, "2025-01-01", False),
        ("exs_changed_2", "2025-01-01", DATE_TO_MAX, False),
        ("exs_deleted_changed", DATE_FROM_MIN, "2025-01-01", True),
        ("exs_deleted_changed", "2025-01-01", DATE_TO_MAX, False),
        ("exs_deleted_unchanged", DATE_FROM_MIN, "2025-01-01", True),
        ("exs_deleted_unchanged", "2025-01-01", DATE_TO_MAX, False),
        ("exs_nondeleted_deleted", DATE_FROM_MIN, "2025-01-01", False),
        ("exs_nondeleted_deleted", "2025-01-01", DATE_TO_MAX, True),
        ("exs_unchanged_1", DATE_FROM_MIN, DATE_TO_MAX, False),
        ("exs_unchanged_2", DATE_FROM_MIN, DATE_TO_MAX, False),
        ("new_1", "2025-01-01", DATE_TO_MAX, False),
        ("new_2", "2025-01-01", DATE_TO_MAX, False),
    ]
    assert bridge_resource.query("resource_cd == 'exs_deleted_changed'")[
        "resource_desc"
    ] == ("Foo", "Bar")
    json_before, json_after = bridge_resource.query(
        "resource_cd == 'exs_nondeleted_deleted'"
    )["resource_json"]
    assert json_before == json_after == existing_nondeleted_deleted_json

    resource_rks, resource_cds = zip(*bridge_resource[["resource_rk", "resource_cd"]])
    assert len(set(resource_rks)) == len(set(resource_cds))


def test_link_resource_source_historization(
    reset_tables: ResetTablesFunc,
    read_table: TableReader,
    resource_repository: ResourceRepository,
):
    reset_tables(
        resources=[
            ResourceRecord(resource_rk=rk, resource_cd=cd)
            for rk, cd in [
                # В resource_cd закодированы комбинации факторов:
                # exc/new -- для этого ключа есть запись в линке?
                # del_t/f -- последняя запись в линке помечена удалённой?
                # stg_eq/ne -- в стейдже source_cd совпадает с source_cd в линке?
                # action_deleted/no_source_system/no_features -- причина, по которой в стейдже не
                # оказалось source_cd?
                (1001, "exs_del_f_action_deleted"),
                (1002, "exs_del_f_no_source_system"),
                (1003, "exs_del_f_no_features"),
                (1004, "exs_del_t_action_deleted"),
                (1005, "exs_del_t_no_source_system"),
                (1006, "exs_del_t_no_features"),
                (2001, "exs_del_f_stg_eq"),
                (2002, "exs_del_f_stg_ne"),
                (2003, "exs_del_t_stg_eq"),
                (2004, "exs_del_t_stg_ne"),
                (3000, "new"),
            ]
        ],
        sources=[
            SourceRecord(source_rk=rk, source_cd=cd)
            for rk, cd in [
                (1010, "exs_del_f_action_deleted"),
                (1020, "exs_del_f_no_source_system"),
                (1030, "exs_del_f_no_features"),
                (1040, "exs_del_t_action_deleted"),
                (1050, "exs_del_t_no_source_system"),
                (1060, "exs_del_t_no_features"),
                (2010, "exs_del_f_stg_eq"),
                (2020, "exs_del_f_stg_ne_old"),
                (2021, "exs_del_f_stg_ne_new"),
                (2030, "exs_del_t_stg_eq"),
                (2040, "exs_del_t_stg_ne_old"),
                (2041, "exs_del_t_stg_ne_new"),
                (3000, "new"),
            ]
        ],
        source_links=[
            SourceLinkRecord(
                resource_rk=resource_rk,
                source_rk=source_rk,
                deleted_flg=deleted_flg,
            )
            for resource_rk, source_rk, deleted_flg in [
                (1001, 1010, False),
                (1002, 1020, False),
                (1003, 1030, False),
                (1004, 1040, True),
                (1005, 1050, True),
                (1006, 1060, True),
                (2001, 2010, False),
                (2002, 2020, False),
                (2003, 2030, True),
                (2004, 2040, True),
            ]
        ],
        resources_staging=[
            # NOTE: экшены "added" и "modified" с т.з. етля одинаковы,
            # поэтому выбор, какой именно поставить для конкретного кейса, произволен
            StageResourceRecord(
                resource_cd="exs_del_f_action_deleted",
                data_action_type_rk=ResourceAction.DELETE,
                resource_type="ceh",
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_no_source_system",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="uni",
                definition={"features": {"source_system": None}},
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_no_features",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
                definition={"features": None},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_action_deleted",
                data_action_type_rk=ResourceAction.DELETE,
                resource_type="uni",
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_no_source_system",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="ceh",
                definition={"features": None},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_no_features",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="uni",
                definition={},
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_stg_eq",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
                definition={"features": {"source_system": "exs_del_f_stg_eq"}},
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_stg_ne",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="uni",
                definition={"features": {"source_system": "exs_del_f_stg_ne_new"}},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_stg_eq",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="ceh",
                definition={"features": {"source_system": "exs_del_t_stg_eq"}},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_stg_ne",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="uni",
                definition={"features": {"source_system": "exs_del_t_stg_ne_new"}},
            ),
            StageResourceRecord(
                resource_cd="new",
                data_action_type_rk=ResourceAction.MODIFY,
                resource_type="ceh",
                definition={"features": {"source_system": "new"}},
            ),
            StageResourceRecord(
                resource_cd="no_such_cd_in_bridge",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="uni",
                definition={"features": {"source_system": "no_such_cd_in_bridge"}},
            ),
        ],
    )

    resource_repository.load_source_links(
        effective_date=datetime(2025, 1, 1),
        stage_table=f"{TEST_SCHEMA}.bridge_resource_staging",
    )
    resource_repository._session.commit()

    link_table = read_table.link_resource_source(
        order_by=["resource_rk", "source_rk", "effective_from_dttm"]
    )

    assert link_table[
        [
            "resource_rk",
            "source_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ]
    ] == [
        # Существущие линки, которых не оказалось в стейдже
        (1001, 1010, DATE_FROM_MIN, "2025-01-01", False),
        (1001, 1010, "2025-01-01", DATE_TO_MAX, True),
        (1002, 1020, DATE_FROM_MIN, "2025-01-01", False),
        (1002, 1020, "2025-01-01", DATE_TO_MAX, True),
        (1003, 1030, DATE_FROM_MIN, "2025-01-01", False),
        (1003, 1030, "2025-01-01", DATE_TO_MAX, True),
        # Существующие линки с deleted_flg=true остались нетронутыми
        (1004, 1040, DATE_FROM_MIN, DATE_TO_MAX, True),
        (1005, 1050, DATE_FROM_MIN, DATE_TO_MAX, True),
        (1006, 1060, DATE_FROM_MIN, DATE_TO_MAX, True),
        # Существующие линки с deleted_flg=false с тем же самым source_cd в стейдже остались
        # нетронутыми
        (2001, 2010, DATE_FROM_MIN, DATE_TO_MAX, False),
        # Существующие линки с deleted_flg=false с отличающимся source_cd в стейдже обновились
        (2002, 2020, DATE_FROM_MIN, "2025-01-01", False),
        (2002, 2021, "2025-01-01", DATE_TO_MAX, False),
        # Существующие линки с deleted_flg=true с тем же самым source_cd в стейдже обновились
        (2003, 2030, DATE_FROM_MIN, "2025-01-01", True),
        (2003, 2030, "2025-01-01", DATE_TO_MAX, False),
        # Существующие линки с deleted_flg=true с отличающимся source_cd в стейдже обновились
        (2004, 2040, DATE_FROM_MIN, "2025-01-01", True),
        (2004, 2041, "2025-01-01", DATE_TO_MAX, False),
        # Новые линки добавились
        (3000, 3000, "2025-01-01", DATE_TO_MAX, False),
        # Новые линки для несуществующих в бридже resource_cd ключей не добавились
    ]


def test_flg_source_priority(
    reset_tables: ResetTablesFunc,
    read_table: TableReader,
    resource_repository: ResourceRepository,
):
    reset_tables(
        resources_staging=[
            # Ресурс с флагами только на корневом уровне (должны игнорироваться, по умолчанию false)
            StageResourceRecord(
                resource_cd="root_flags_only",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
                definition={
                    "resource_desc": "Test resource with root-level flags",
                    "is_readonly": True,
                    "is_maintenance": True,
                },
            ),
            # Ресурс с флагами только в status (должны использоваться)
            StageResourceRecord(
                resource_cd="status_flags_only",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="uni",
                definition={
                    "resource_desc": "Test resource with status-level flags",
                    "status": {
                        "is_readonly": True,
                        "is_maintenance": True,
                    },
                },
            ),
            # Ресурс с флагами в обоих местах (status должен иметь приоритет)
            StageResourceRecord(
                resource_cd="both_flags_conflict",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="ceh",
                definition={
                    "resource_desc": "Test resource with conflicting flag values",
                    "is_readonly": False,
                    "is_maintenance": False,
                    "status": {
                        "is_readonly": True,
                        "is_maintenance": True,
                    },
                },
            ),
            # Ресурс без флагов вообще (должны по умолчанию быть false)
            StageResourceRecord(
                resource_cd="no_flags",
                data_action_type_rk=ResourceAction.ADD,
                resource_type="uni",
                definition={
                    "resource_desc": "Test resource with no flags",
                },
            ),
        ],
    )

    resource_repository.load_resources(
        version_id=1,
        effective_date=datetime(2025, 1, 1),
        stage_table=f"{TEST_SCHEMA}.bridge_resource_staging",
    )
    resource_repository._session.commit()

    bridge_resource = read_table.bridge_resource(order_by=["resource_cd"])
    assert bridge_resource[
        ["resource_cd", "is_readonly_flg", "is_maintenance_flg"]
    ] == [
        ("both_flags_conflict", True, True),
        ("no_flags", False, False),
        ("root_flags_only", False, False),
        ("status_flags_only", True, True),
    ]


def test_link_resource_table_historization(
    reset_tables: ResetTablesFunc,
    read_table: TableReader,
    resource_repository: ResourceRepository,
):
    reset_tables(
        resources=[
            ResourceRecord(resource_rk=rk, resource_cd=cd)
            for rk, cd in [
                # В resource_cd закодированы следующие комбинации факторов:
                # exs/new -- есть ли у этого ресурса существующие записи в ссылке?
                # del_t/f -- отмечена ли последняя запись в ссылке как удаленная?
                # single/multi -- ссылается ли ресурс на одну таблицу или на несколько таблиц?
                # stg_same/diff -- в стейджинге у ресурса те же таблицы или другие?
                # Существующая связь, которая должна быть удалена (нет таблиц в стейджинге)
                (1001, "exs_del_f_single_stg_none"),
                # Существующие множественные связи, которые должны быть удалены
                # (нет таблиц в стейджинге)
                (1002, "exs_del_f_multi_stg_none"),
                # Существующая удаленная связь (должна остаться нетронутой)
                (1003, "exs_del_t_single_stg_none"),
                # Существующие множественные удаленные связи (должны остаться нетронутыми)
                (1004, "exs_del_t_multi_stg_none"),
                # Существующая связь с той же таблицей в стейджинге (без изменений)
                (1005, "exs_del_f_single_stg_same"),
                # Существующие множественные связи с теми же таблицами в стейджинге
                # (без изменений)
                (1006, "exs_del_f_multi_stg_same"),
                # Существующая связь с другой таблицей в стейджинге (обновление)
                (1007, "exs_del_f_single_stg_diff"),
                # Существующие связи с другими таблицами в стейджинге (обновление)
                (1008, "exs_del_f_multi_stg_diff"),
                # Существующие связи с частичным совпадением в стейджинге
                # (некоторые остаются, некоторые удаляются, некоторые добавляются)
                (1009, "exs_del_f_multi_stg_partial"),
                # Существующая удаленная связь с той же таблицей в стейджинге
                # (должна быть восстановлена)
                (1010, "exs_del_t_single_stg_same"),
                # Существующие множественные удаленные связи с теми же таблицами в стейджинге
                # (должны быть восстановлены)
                (1011, "exs_del_t_multi_stg_same"),
                # Существующая удаленная связь с другой таблицей в стейджинге
                (1012, "exs_del_t_single_stg_diff"),
                # Существующие множественные удаленные связи с другими таблицами в стейджинге
                (1013, "exs_del_t_multi_stg_diff"),
                # Новый ресурс с одной таблицей
                (2001, "new_single"),
                # Новый ресурс с несколькими таблицами
                (2002, "new_multi"),
                # Ресурс с дублированными ссылками на таблицы
                (2003, "duplicate_tables"),
                # Ресурс с несуществующими таблицами
                (2004, "nonexistent_tables"),
                # Ресурс для тестирования 'null' строки в datasets
                (2005, "null_datasets"),
            ]
        ],
        tables=[
            TableRecord(table_rk=rk, table_name=name, schema_name=schema)
            for rk, name, schema in [
                (1001, "table1", "schema1"),
                (1002, "table2", "schema1"),
                (1003, "table3", "schema1"),
                (1004, "table4", "schema1"),
                (1005, "table5", "schema1"),
                (1006, "table6", "schema2"),
                (1007, "table7", "schema2"),
                (1008, "table8", "schema2"),
                (1009, "table9", "schema2"),
                (1010, "table10", "schema2"),
            ]
        ],
        table_links=[
            TableLinkRecord(
                resource_rk=resource_rk,
                table_rk=table_rk,
                deleted_flg=deleted_flg,
            )
            for resource_rk, table_rk, deleted_flg in [
                # Cвязи c одной таблицей
                # Будет удалена
                (1001, 1001, False),
                # Удаленная, останется неизменной
                (1003, 1003, True),
                # Останется неизменной
                (1005, 1005, False),
                # Будет заменена на другую
                (1007, 1007, False),
                # Удаленная, будет восстановлена с тем же table_rk
                (1010, 1010, True),
                # Удаленная, будет восстановлена с другим table_rk
                (1012, 1002, True),
                # Cвязи c несколькими таблицами
                # Все будут удалены
                (1002, 1001, False),
                (1002, 1002, False),
                # Все уже удалены
                (1004, 1003, True),
                (1004, 1004, True),
                # Останутся неизменными
                (1006, 1005, False),
                (1006, 1006, False),
                # Будут заменены на другие
                (1008, 1007, False),
                (1008, 1008, False),
                # Некоторые останутся, некоторые будут удалены, некоторые добавлены
                (1009, 1001, False),
                (1009, 1002, False),
                (1009, 1003, False),
                # Все уже удалены, будут восстановлены с теми же table_rk
                (1011, 1001, True),
                (1011, 1002, True),
                # Все уже удалены, будут восстановлены с другими table_rk
                (1013, 1003, True),
                (1013, 1004, True),
            ]
        ],
        resources_staging=[
            # Ресурсы без таблиц в стейджинге (будут удалены существующие)
            StageResourceRecord(
                resource_cd="exs_del_f_single_stg_none",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={},
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_multi_stg_none",
                data_action_type_rk=ResourceAction.ADD,
                definition={},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_single_stg_none",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_multi_stg_none",
                data_action_type_rk=ResourceAction.ADD,
                definition={},
            ),
            # Ресурсы с теми же таблицами в стейджинге (останутся неизменными)
            StageResourceRecord(
                resource_cd="exs_del_f_single_stg_same",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={"datasets": [{"name": "table5", "schema_name": "schema1"}]},
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_multi_stg_same",
                data_action_type_rk=ResourceAction.ADD,
                definition={
                    "datasets": [
                        {"name": "table5", "schema_name": "schema1"},
                        {"name": "table6", "schema_name": "schema2"},
                    ]
                },
            ),
            # Ресурсы с другими таблицами в стейджинге (будут обновлены)
            StageResourceRecord(
                resource_cd="exs_del_f_single_stg_diff",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={"datasets": [{"name": "table8", "schema_name": "schema2"}]},
            ),
            StageResourceRecord(
                resource_cd="exs_del_f_multi_stg_diff",
                data_action_type_rk=ResourceAction.ADD,
                definition={
                    "datasets": [
                        {"name": "table9", "schema_name": "schema2"},
                        {"name": "table10", "schema_name": "schema2"},
                    ]
                },
            ),
            # Ресурс с частичным совпадением в стейджинге (будут обновлены)
            StageResourceRecord(
                resource_cd="exs_del_f_multi_stg_partial",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={
                    "datasets": [
                        {"name": "table1", "schema_name": "schema1"},  # Не изменится
                        {"name": "table4", "schema_name": "schema1"},  # Будут добавлены
                        {"name": "table5", "schema_name": "schema1"},
                    ]
                },
            ),
            # Ресурсы с удаленными связями, которые будут восстановлены
            StageResourceRecord(
                resource_cd="exs_del_t_single_stg_same",
                data_action_type_rk=ResourceAction.ADD,
                definition={
                    "datasets": [{"name": "table10", "schema_name": "schema2"}]
                },
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_multi_stg_same",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={
                    "datasets": [
                        {"name": "table1", "schema_name": "schema1"},
                        {"name": "table2", "schema_name": "schema1"},
                    ]
                },
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_single_stg_diff",
                data_action_type_rk=ResourceAction.ADD,
                definition={"datasets": [{"name": "table4", "schema_name": "schema1"}]},
            ),
            StageResourceRecord(
                resource_cd="exs_del_t_multi_stg_diff",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={
                    "datasets": [
                        {"name": "table5", "schema_name": "schema1"},
                        {"name": "table6", "schema_name": "schema2"},
                    ]
                },
            ),
            # Новые ресурсы
            StageResourceRecord(
                resource_cd="new_single",
                data_action_type_rk=ResourceAction.ADD,
                definition={"datasets": [{"name": "table1", "schema_name": "schema1"}]},
            ),
            StageResourceRecord(
                resource_cd="new_multi",
                data_action_type_rk=ResourceAction.MODIFY,
                definition={
                    "datasets": [
                        {"name": "table1", "schema_name": "schema1"},
                        {"name": "table2", "schema_name": "schema1"},
                        {"name": "table3", "schema_name": "schema1"},
                    ]
                },
            ),
            # Ресурс, которого нет в bridge_resource
            StageResourceRecord(
                resource_cd="no_such_cd_in_bridge",
                data_action_type_rk=ResourceAction.ADD,
                definition={"datasets": [{"name": "table1", "schema_name": "schema1"}]},
            ),
            # Ресурс с дублированными ссылками на таблицы
            StageResourceRecord(
                resource_cd="duplicate_tables",
                data_action_type_rk=ResourceAction.ADD,
                definition={
                    "datasets": [
                        {"name": "table1", "schema_name": "schema1"},
                        {"name": "table1", "schema_name": "schema1"},
                        {"name": "table2", "schema_name": "schema1"},
                        {"name": "table2", "schema_name": "schema1"},
                    ]
                },
            ),
            # Ресурс с несуществующими таблицами
            StageResourceRecord(
                resource_cd="nonexistent_tables",
                data_action_type_rk=ResourceAction.ADD,
                definition={
                    "datasets": [
                        {"name": "nonexistent1", "schema_name": "schema1"},
                        {"name": "nonexistent2", "schema_name": "schema2"},
                        {"name": "table1", "schema_name": "schema1"},
                    ]
                },
            ),
            # Ресурс с 'null' строкой в datasets
            StageResourceRecord(
                resource_cd="null_datasets",
                data_action_type_rk=ResourceAction.ADD,
                definition={"datasets": None},
            ),
        ],
    )

    resource_repository.load_table_links(
        effective_date=datetime(2025, 1, 1),
        stage_table=f"{TEST_SCHEMA}.bridge_resource_staging",
    )
    resource_repository._session.commit()

    link_table = read_table.link_resource_table(
        order_by=["resource_rk", "table_rk", "effective_from_dttm"]
    )

    assert link_table[
        [
            "resource_rk",
            "table_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ]
    ] == [
        # Одна таблица, будет удалена (ресурс без таблиц в стейджинге)
        (1001, 1001, DATE_FROM_MIN, "2025-01-01", False),
        (1001, 1001, "2025-01-01", DATE_TO_MAX, True),
        # Несколько таблиц, будут удалены (ресурс без таблиц в стейджинге)
        (1002, 1001, DATE_FROM_MIN, "2025-01-01", False),
        (1002, 1001, "2025-01-01", DATE_TO_MAX, True),
        (1002, 1002, DATE_FROM_MIN, "2025-01-01", False),
        (1002, 1002, "2025-01-01", DATE_TO_MAX, True),
        # Существующие удаленные связи остаются неизменными
        (1003, 1003, DATE_FROM_MIN, DATE_TO_MAX, True),
        (1004, 1003, DATE_FROM_MIN, DATE_TO_MAX, True),
        (1004, 1004, DATE_FROM_MIN, DATE_TO_MAX, True),
        # Существующая связь с той же таблицей (без изменений)
        (1005, 1005, DATE_FROM_MIN, DATE_TO_MAX, False),
        # Существующие множественные связи с той же таблицей (без изменений)
        (1006, 1005, DATE_FROM_MIN, DATE_TO_MAX, False),
        (1006, 1006, DATE_FROM_MIN, DATE_TO_MAX, False),
        # Существующая связь заменена на другую
        (1007, 1007, DATE_FROM_MIN, "2025-01-01", False),
        (1007, 1007, "2025-01-01", DATE_TO_MAX, True),
        (1007, 1008, "2025-01-01", DATE_TO_MAX, False),
        # Существующие множественные связи заменены на другие
        (1008, 1007, DATE_FROM_MIN, "2025-01-01", False),
        (1008, 1007, "2025-01-01", DATE_TO_MAX, True),
        (1008, 1008, DATE_FROM_MIN, "2025-01-01", False),
        (1008, 1008, "2025-01-01", DATE_TO_MAX, True),
        (1008, 1009, "2025-01-01", DATE_TO_MAX, False),
        (1008, 1010, "2025-01-01", DATE_TO_MAX, False),
        # Ресурс с частичным совпадением в стейджинге
        (1009, 1001, DATE_FROM_MIN, DATE_TO_MAX, False),  # Сохранена
        (1009, 1002, DATE_FROM_MIN, "2025-01-01", False),  # Удалена
        (1009, 1002, "2025-01-01", DATE_TO_MAX, True),
        (1009, 1003, DATE_FROM_MIN, "2025-01-01", False),  # Удалена
        (1009, 1003, "2025-01-01", DATE_TO_MAX, True),
        (1009, 1004, "2025-01-01", DATE_TO_MAX, False),  # Добавлена
        (1009, 1005, "2025-01-01", DATE_TO_MAX, False),  # Добавлена
        # Существующие удаленные связи восстановлены с теми же table_rk
        (1010, 1010, DATE_FROM_MIN, "2025-01-01", True),
        (1010, 1010, "2025-01-01", DATE_TO_MAX, False),
        (1011, 1001, DATE_FROM_MIN, "2025-01-01", True),
        (1011, 1001, "2025-01-01", DATE_TO_MAX, False),
        (1011, 1002, DATE_FROM_MIN, "2025-01-01", True),
        (1011, 1002, "2025-01-01", DATE_TO_MAX, False),
        # Существующие удаленные связи восстановлены с другими table_rk
        (
            1012,
            1002,
            DATE_FROM_MIN,
            DATE_TO_MAX,
            True,
        ),  # Оригинальная связь остается удаленной
        (1012, 1004, "2025-01-01", DATE_TO_MAX, False),  # Новая связь добавлена
        (
            1013,
            1003,
            DATE_FROM_MIN,
            DATE_TO_MAX,
            True,
        ),  # Оригинальные связи остаются удаленными
        (1013, 1004, DATE_FROM_MIN, DATE_TO_MAX, True),
        (1013, 1005, "2025-01-01", DATE_TO_MAX, False),  # Новые связи добавлены
        (1013, 1006, "2025-01-01", DATE_TO_MAX, False),
        # Новые ресурсы с таблицами
        (2001, 1001, "2025-01-01", DATE_TO_MAX, False),
        (2002, 1001, "2025-01-01", DATE_TO_MAX, False),
        (2002, 1002, "2025-01-01", DATE_TO_MAX, False),
        (2002, 1003, "2025-01-01", DATE_TO_MAX, False),
        # Ресурс с дублированными ссылками на таблицы
        # (создаться только одна связь на уникальную table_rk)
        (2003, 1001, "2025-01-01", DATE_TO_MAX, False),
        (2003, 1002, "2025-01-01", DATE_TO_MAX, False),
        # Ресурс с несуществующими таблицами
        # (создадуться связи только для существующих table_rk)
        (2004, 1001, "2025-01-01", DATE_TO_MAX, False),
    ]
