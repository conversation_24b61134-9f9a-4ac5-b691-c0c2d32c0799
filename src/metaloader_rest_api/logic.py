from datetime import datetime, timedelta, timezone
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Mapping,
    Optional,
    Sequence,
    Tuple,
    Type,
    TypeVar,
    Union,
)
from uuid import UUID

from celery import Celery
from fastapi import HTTPException, Request, status
from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON>ogger

from metaloader_rest_api import master_flow_repository, models, schemas
from metaloader_rest_api.af_defaults import AfDefaults
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.logging import pass_metamodel_log_kwargs
from metaloader_rest_api.master_files_schemas import ControlFlow as MasterFlow
from metaloader_rest_api.master_flow_check import check_flow
from metaloader_rest_api.master_flow_repository import load_master_flows
from metaloader_rest_api.models import (
    TransactionResolutionDetails,
    TransactionStatus,
)
from metaloader_rest_api.resource_repository import ResourceStageSequentialRepository
from metaloader_rest_api.schemas import ReleaseMetadata
from metaloader_rest_api.vcs_repository_version_repository import (
    get_vsc_repository_revision_id,
)

_TRANSACTION_MODE_S2M = {
    schemas.TransactionMode.SNAPSHOT: models.TransactionMode.SNAPSHOT,
    schemas.TransactionMode.INCREMENTAL: models.TransactionMode.INCREMENT,
}
_TRANSACTION_STATUS_S2M = {
    schemas.TransactionStatus.OPENED: models.TransactionStatus.OPENED,
    schemas.TransactionStatus.COMMITTED: models.TransactionStatus.COMMITTED,
    schemas.TransactionStatus.ROLLEDBACK: models.TransactionStatus.ROLLEDBACK,
    schemas.TransactionStatus.IN_PROGRESS: models.TransactionStatus.IN_PROGRESS,
    schemas.TransactionStatus.ETL_ERROR: models.TransactionStatus.ETL_ERROR,
}
_TRANSACTION_STATUS_M2S = {v: k for k, v in _TRANSACTION_STATUS_S2M.items()}
_TRANSACTION_ACTION_M2S = {
    models.MasterFlowAction.ADD: schemas.MasterFlowAction.ADD,
    models.MasterFlowAction.MODIFY: schemas.MasterFlowAction.MODIFY,
    models.MasterFlowAction.RENAME: schemas.MasterFlowAction.RENAME,
    models.MasterFlowAction.DELETE: schemas.MasterFlowAction.DELETE,
}


CELERY_QUEUE_SEQUENTIAL = "sequential"
CELERY_QUEUE_PARALLEL = "parallel"
CELERY_QUEUE_DEFAULT = CELERY_QUEUE_PARALLEL


def run_release_sources_integration_etl(
    session: Session,
    tx_uid: UUID,
    integration_mode: models.TransactionMode,
) -> Optional[Dict[str, List[str]]]:
    return load_master_flows(session=session, tx_id=tx_uid)


def dry_run_release_sources_integration_etl(
    session: Session,
    tx_uid: UUID,
    integration_mode: models.TransactionMode,
) -> Optional[Dict[str, List[str]]]:
    return load_master_flows(session=session, tx_id=tx_uid, commit=False)


_ConcreteTransaction = TypeVar(
    "_ConcreteTransaction",
    bound=Union[models.MasterFlowTransaction, models.ResourceTransaction],
)


def query_transaction(
    tx_uid: UUID,
    tx_type: Type[_ConcreteTransaction],
    db: Session,
    request: Request,
) -> _ConcreteTransaction:
    transaction = (
        db.query(tx_type).filter(tx_type.id == tx_uid).first()  # noqa
    )
    if not transaction:
        err_msg = "Transaction not found"
        pass_metamodel_log_kwargs(request, error=err_msg)
        raise HTTPException(status_code=404, detail=err_msg)

    return transaction


def ensure_transaction_is_opened(
    transaction: models.BaseTransaction, request: Request
) -> None:
    if transaction.status == models.TransactionStatus.OPENED.value:
        return

    try:
        tx_status = models.TransactionStatus(transaction.status).name.lower()
    except ValueError:
        tx_status = str(transaction.status)

    err_msg = "Attempting to modify a transaction that is not open"
    pass_metamodel_log_kwargs(request, tx_status=tx_status, error=err_msg)
    raise HTTPException(
        status_code=400,
        detail=err_msg,
    )


def ensure_master_flow_valid(
    master_flow: Optional[MasterFlow], request: Request
) -> None:
    if master_flow is None:
        return

    result = check_flow(master_flow)
    if result:
        pass_metamodel_log_kwargs(request, error="invalid master flow")
        raise HTTPException(
            status_code=status.HTTP_406_NOT_ACCEPTABLE,
            detail={f"Master Flow {master_flow.master_flow_name} is not valid": result},
        )


def get_missing_master_flow_names(
    transaction: models.MasterFlowTransaction,
    expected_master_flows: List[str],
) -> List[str]:
    # FIX-ME: N+1 query
    created_in_the_transaction = set(cf.name for cf in transaction.master_flows)
    missing_master_flows = [
        name for name in expected_master_flows if name not in created_in_the_transaction
    ]
    return missing_master_flows


def update_transaction_instance_on_resolution(
    transaction: models.BaseTransaction,
    status: schemas.TransactionStatus,
    mode: Optional[schemas.TransactionMode] = None,
    expected_master_flows: Optional[List[str]] = None,
    master_flow_errors: Optional[Dict[str, List[str]]] = None,
) -> None:
    now = models.now_at_utc()

    transaction.resolved_at = now
    transaction.status_changed_at = now
    transaction.status = _TRANSACTION_STATUS_S2M[status].value

    if mode:
        transaction.mode = _TRANSACTION_MODE_S2M[mode].value
    if expected_master_flows:
        transaction.expected_master_flows = expected_master_flows
    if master_flow_errors:
        transaction.resolution_details = models.TransactionResolutionDetails(
            master_flow_errors=master_flow_errors,
        ).model_dump()


def get_master_flow_transaction_info_response(
    transaction: models.MasterFlowTransaction,
) -> schemas.TransactionInfo:
    return schemas.TransactionInfo(
        uid=transaction.id,
        status=_TRANSACTION_STATUS_M2S[transaction.status],  # type: ignore
        release_metadata=transaction.release_metadata,
        expected_master_flows=transaction.expected_master_flows,
        submitted_master_flows={cf.name: None for cf in transaction.master_flows},
        result=transaction.resolution_details,
    )


def get_resource_transaction_info_response(
    session: Session,
    transaction: models.ResourceTransaction,
) -> schemas.ResourceTransactionInfo:
    resource_cds = ResourceStageSequentialRepository(
        session=session,
        load_id=transaction.id,
    ).get_resource_cds()
    return schemas.ResourceTransactionInfo(
        uid=transaction.id,
        status=_TRANSACTION_STATUS_M2S[transaction.status],  # type: ignore
        release_metadata=transaction.release_metadata,
        resources=resource_cds,
    )


def get_release_metadata(
    db_session: Session,
    module: str,
) -> ReleaseMetadata:
    vsc_repository_revision_id = get_vsc_repository_revision_id(
        session=db_session,
        vsc_repository="DTPL/ceh-config",
        module=module,
    )
    if vsc_repository_revision_id is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Metadata for {module} not found",
        )
    return ReleaseMetadata(
        release_module=module,
        release_num="0.0.0",
        git_revision_hash=vsc_repository_revision_id,
        effective_from_date=LAST_DATE,
    )


def get_master_flow_names(db: Session, module: str) -> Sequence[str]:
    return master_flow_repository.get_master_flow_names(db, module)


def query_transaction_master_flow_action(
    name: str,
    transaction: models.MasterFlowTransaction,
    db: Session,
) -> Optional[schemas.MasterFlowAction]:
    master_flow = (
        db.query(models.MasterFlowTransactionItem)
        .with_entities(models.MasterFlowTransactionItem.action)
        .filter(models.MasterFlowTransactionItem.transaction_id == transaction.id)
        .filter(models.MasterFlowTransactionItem.name == name)  # noqa
        .one_or_none()
    )
    return _TRANSACTION_ACTION_M2S[master_flow.action] if master_flow else None


def execute_transactions_timeout(
    get_session: Callable[[], Session],
    logger: BoundLogger,
    logger_kwargs: Optional[Dict[str, str]] = None,
) -> None:
    """Проставляет status=rolledback для всех транзакций, у которых превышено время таймаута

    Предполагается, что эта функция будет выполняться в отдельном треде тредпула.
    """
    with get_session() as session:
        # NOTE: Мы никак не проверяем, не выполняется ли всё ещё вью-функция
        # эндпойнта /commit или /try. Делаем допущение, что к моменту таймаута
        # уже она уже точно завершится.
        stmt = text(
            """
            UPDATE metamodel.service_transaction_status
            SET service_transaction_status_rk = :rolledback_status
            WHERE service_transaction_status_rk IN (:opened_status, :in_progress_status)
              AND timezone('UTC', now()) > (
                request_dttm + (transaction_timeout_duration || ' sec')::interval
              )
            RETURNING service_transaction_uid
            """
        )
        result = session.execute(
            statement=stmt,
            params={
                "rolledback_status": models.TransactionStatus.ROLLEDBACK.value,
                "opened_status": models.TransactionStatus.OPENED.value,
                "in_progress_status": models.TransactionStatus.IN_PROGRESS.value,
            },
        )
        session.commit()
        affected_rows_count = result.rowcount
    # TO-DO: убрать повторение логики переименования лог-аргументов
    kwargs = {
        "metamodel.tx_count": affected_rows_count,
    }
    if logger_kwargs:
        kwargs.update((f"metamodel.{k}", v) for k, v in logger_kwargs.items())  # type: ignore
    logger.info(
        f"Background task rolledback {affected_rows_count} transactions by timeout"
        if affected_rows_count
        else "No timedout transactions found",
        **kwargs,
    )


def create_resource_actions_staging_table(
    session: Session,
    tx_uid: UUID,
) -> None:
    repo = ResourceStageSequentialRepository(session=session, load_id=tx_uid)
    repo.drop_table()
    repo.create_table()


def initiate_loading_flows_from_airflow(
    celery_client: Celery,
    db_session: Session,
    logger: BoundLogger,
    module: str,
    version: str,
    af_url: str,
    page_size: int = AfDefaults.DAG_PAGE_SIZE.value,
    keep_dag: bool = True,
    with_tasks: bool = True,
    parse_master_flows: bool = True,
    limit: int = AfDefaults.DAG_LIMIT.value,
    webhook: Optional[str] = None,
) -> UUID:
    log = logger.bind(action="load_flow_from_af", module=module, version=version)

    load_id, effective_date = create_tx(
        db_session=db_session,
        logger=log,
        timeout=timedelta(hours=3),
    )

    log.info(
        "init",
        load_id=load_id,
        effective_date=effective_date,
        af_url=af_url,
        page_size=page_size,
        keep_dag=keep_dag,
        with_tasks=with_tasks,
        parse_master_flows=parse_master_flows,
        limit=limit,
        webhook=webhook,
    )

    celery_client.send_task(
        name="load_flows_from_airflow",
        kwargs={
            "load_id": str(load_id),
            "module": module,
            "version": version,
            "effective_date": effective_date,
            "af_url": af_url,
            "page_size": page_size,
            "keep_dag": keep_dag,
            "with_tasks": with_tasks,
            "parse_master_flows": parse_master_flows,
            "limit": limit,
            "webhook": webhook,
        },
        queue="sequential",
    )

    return load_id


def initiate_transactions_statuses_check(
    celery_client: Celery,
    db_session: Session,
    transactions_ids: Tuple[UUID, ...],
    logger: BoundLogger,
) -> UUID:
    log = logger.bind(action="check_transactions_statuses")

    load_id, effective_date = create_tx(
        db_session=db_session,
        logger=log,
        timeout=timedelta(hours=3),
    )

    log.info(
        event="init",
        load_id=load_id,
        effective_date=effective_date,
        transactions_ids=transactions_ids,
    )

    process_tx(db_session, logger, load_id)

    celery_client.send_task(
        name="check_transactions_statuses",
        kwargs={
            "load_id": str(load_id),
            "transactions_ids": tuple(map(str, transactions_ids)),
        },
        queue="sequential",
    )

    return load_id


def send_task(
    name: str,
    params: Mapping[str, Any],
    queue: str,
    celery: Celery,
    db_session: Session,
    log: BoundLogger,
) -> UUID:
    log = log.bind(action="send_task", name=name)

    log.info("begin")
    load_id, effective_date = create_tx(db_session, log)
    log = log.bind(load_id=load_id, effective_date=effective_date)

    log.info("send", params=params, queue=queue)
    celery.send_task(
        name=name,
        kwargs={
            "load_id": load_id,
            "effective_date": effective_date,
            **params,
        },
        queue=queue,
    )
    log.info("end")

    return load_id


def create_tx(
    db_session: Session,
    logger: BoundLogger,
    timeout: timedelta = models.BaseTransaction.TIMEOUT_DEFAULT,
) -> Tuple[UUID, datetime]:
    tx = models.MasterFlowTransaction(
        status=models.TransactionStatus.OPENED.value,
        timeout_sec=timeout.seconds,
    )
    db_session.add(tx)
    db_session.commit()

    logger.info("create_tx", load_id=tx.id, effective_date=tx.created_at)

    return tx.id, tx.created_at


def close_tx(
    db_session: Session,
    logger: BoundLogger,
    tx_id: UUID,
    rollback: bool = False,
    message: Optional[str] = None,
    actual_date: Optional[datetime] = None,
) -> None:
    set_tx_state(
        db_session=db_session,
        tx_id=tx_id,
        status=(
            models.TransactionStatus.ROLLEDBACK
            if rollback
            else models.TransactionStatus.COMMITTED
        ),
        result=models.TransactionResolutionDetails(message=message),
        actual_date=actual_date,
    )

    logger.info("close_tx", rollback=rollback, message=message)


def process_tx(
    db_session: Session,
    logger: BoundLogger,
    tx_id: UUID,
    actual_date: Optional[datetime] = None,
) -> None:
    set_tx_state(
        db_session=db_session,
        tx_id=tx_id,
        status=models.TransactionStatus.IN_PROGRESS,
        actual_date=actual_date,
    )

    logger.info("process_tx")


def set_tx_state(
    db_session: Session,
    tx_id: UUID,
    status: TransactionStatus,
    result: Optional[TransactionResolutionDetails] = None,
    actual_date: Optional[datetime] = None,
) -> None:
    if actual_date is None:
        actual_date = datetime.now(tz=timezone.utc)

    (
        db_session.query(models.MasterFlowTransaction)
        .filter(models.MasterFlowTransaction.id == tx_id)
        .update(
            {
                models.MasterFlowTransaction.status: status.value,
                models.MasterFlowTransaction.status_changed_at: actual_date,
                models.MasterFlowTransaction.resolution_details: (
                    result.model_dump(exclude_defaults=True) if result else None
                ),
                models.MasterFlowTransaction.resolved_at: (
                    actual_date if result is not None else None
                ),
            }
        )
    )
    db_session.commit()
