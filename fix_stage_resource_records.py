#!/usr/bin/env python3
"""
Script to add resource_type="ceh" to StageResourceRecord instances that don't have it.
"""

import re
import sys

def fix_stage_resource_records(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match StageResourceRecord instances that don't have resource_type
    # This pattern looks for StageResourceRecord( followed by parameters, but not containing resource_type
    pattern = r'(StageResourceRecord\(\s*\n\s*resource_cd="[^"]+",\s*\n\s*data_action_type_rk=ResourceAction\.[A-Z]+,)(\s*\n(?!\s*resource_type))'
    
    def replacement(match):
        prefix = match.group(1)
        suffix = match.group(2)
        return f'{prefix}\n                resource_type="ceh",{suffix}'
    
    # Apply the replacement
    new_content = re.sub(pattern, replacement, content)
    
    # Also handle cases where definition comes right after data_action_type_rk
    pattern2 = r'(StageResourceRecord\(\s*\n\s*resource_cd="[^"]+",\s*\n\s*data_action_type_rk=ResourceAction\.[A-Z]+,)(\s*\n\s*definition=)'
    
    def replacement2(match):
        prefix = match.group(1)
        suffix = match.group(2)
        return f'{prefix}\n                resource_type="ceh",{suffix}'
    
    new_content = re.sub(pattern2, replacement2, new_content)
    
    # Handle cases where the StageResourceRecord ends right after data_action_type_rk
    pattern3 = r'(StageResourceRecord\(\s*\n\s*resource_cd="[^"]+",\s*\n\s*data_action_type_rk=ResourceAction\.[A-Z]+,)(\s*\n\s*\),)'
    
    def replacement3(match):
        prefix = match.group(1)
        suffix = match.group(2)
        return f'{prefix}\n                resource_type="ceh",{suffix}'
    
    new_content = re.sub(pattern3, replacement3, new_content)
    
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"Updated {file_path}")
        return True
    else:
        print(f"No changes needed in {file_path}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python fix_stage_resource_records.py <file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    fix_stage_resource_records(file_path)
