from typing import Any, Iterator, Optional, Tuple, Type

from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.yaml.yaml_loader import Yaml

_DEFAULT_LOGGER = get_logger(__name__)


class YamlWrapper:
    def __init__(
        self,
        yaml: Yaml,
        path: Optional[str] = "$",
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        super().__init__()

        self._yaml = yaml
        self._log = log
        self._path = path

    def has_attributes(
        self,
        quiet: bool = False,
    ) -> bool:
        return self._is_type(dict, quiet)

    def has_items(
        self,
        quiet: bool = False,
    ) -> bool:
        return self._is_type(list, quiet)

    def _is_type(
        self,
        type_: Type,
        quiet: bool = False,
    ) -> bool:
        if isinstance(self._yaml, type_):
            return True
        elif quiet:
            return False

        self._log.error(
            "invalid_attribute",
            path=self._path,
            value=self._yaml,
            type=type(self._yaml).__name__,
        )
        return False

    def as_string(self) -> Optional[str]:
        if self._yaml is None:
            return None

        if isinstance(self._yaml, str):
            return self._yaml

        self._log.error(
            "invalid_attribute",
            path=self._path,
            value=self._yaml,
            type=type(self._yaml).__name__,
        )
        return None

    def as_iterator(self) -> Iterator["YamlWrapper"]:
        if not isinstance(self._yaml, list):
            self._log.error(
                "invalid_attribute",
                path=self._path,
                value=self._yaml,
                type=type(self._yaml).__name__,
            )
            return

        for index, yaml in enumerate(self._yaml):
            yield YamlWrapper(
                yaml=yaml,
                path=f"{self._path}[{index}]",
                log=self._log,
            )

    def as_iterator_with_id(
        self,
        id_attribute: str = "id",
    ) -> Iterator["YamlWrapper"]:
        if not isinstance(self._yaml, list):
            self._log.error(
                "invalid_attribute",
                path=self._path,
                value=self._yaml,
                type=type(self._yaml).__name__,
            )
            return

        for index, yaml in enumerate(self._yaml):
            if not isinstance(yaml, dict):
                self._log.error(
                    "invalid_attribute",
                    path=f"{self._path}[{index}]",
                    value=yaml,
                    type=type(yaml).__name__,
                )
                continue
            yield YamlWrapper(
                yaml=yaml,
                path=f"{self._path}[{id_attribute}={yaml.get(id_attribute, index)}]",
                log=self._log,
            )

    def as_item_iterator(self) -> Iterator[Tuple[str, "YamlWrapper"]]:
        if not isinstance(self._yaml, dict):
            self._log.error(
                "invalid_attribute",
                path=self._path,
                value=self._yaml,
                type=type(self._yaml).__name__,
            )
            return

        for name, yaml in self._yaml.items():
            yield (
                name,
                YamlWrapper(
                    yaml=yaml,
                    path=f"{self._path}[{name}]",
                    log=self._log,
                ),
            )

    def as_value_iterator(self) -> Iterator[Yaml]:
        if isinstance(self._yaml, str):
            yield self._yaml
        elif isinstance(self._yaml, list):
            for yaml in self._yaml:
                yield yaml
        else:
            self._log.error(
                "invalid_attribute",
                path=self._path,
                value=self._yaml,
                type=type(self._yaml).__name__,
            )
            yield from ()

    def get(
        self,
        name: str,
    ) -> Optional["YamlWrapper"]:
        yaml = self._yaml.get(name)
        if yaml:
            return YamlWrapper(
                yaml=yaml,
                path=f"{self._path}.{name}",
                log=self._log,
            )

        self._log.error(
            "missing_attribute",
            attribute=name,
            path=self._path,
        )
        return None

    def get_with_legacy(
        self,
        name: str,
        legacy_name: str,
    ) -> Tuple[Optional["YamlWrapper"], bool]:
        for attribute, non_legacy in ((name, True), (legacy_name, False)):
            if attribute is None:
                continue
            yaml = self._yaml.get(attribute)
            if yaml:
                return (
                    YamlWrapper(
                        yaml=yaml,
                        path=f"{self._path}.{attribute}",
                        log=self._log,
                    ),
                    non_legacy,
                )

        self._log.error(
            "missing_attribute",
            attribute=name,
            legacy_attribute=legacy_name,
            path=self._path,
        )
        return None, True

    def get_item(
        self,
        index: int,
    ) -> Yaml:
        if index >= len(self._yaml):
            self._log.error("missing_attribute", attribute=index, path=self._path)
        return self._yaml[index]

    def get_attribute(
        self,
        key: str,
    ) -> Yaml:
        value = self.get_attribute_with_default(key)
        if value is None:
            self._log.error("missing_attribute", attribute=key, path=self._path)
        return value

    def get_attribute_with_default(
        self,
        key: str,
        default: Optional[str] = None,
    ) -> Yaml:
        return self._yaml.get(key, default)

    def get_attribute_with_legacy(
        self,
        name: str,
        legacy_name: str,
    ) -> Yaml:
        for attribute, non_legacy in ((name, True), (legacy_name, False)):
            if attribute is None:
                continue
            yaml = self._yaml.get(attribute)
            if yaml:
                return yaml

        self._log.error(
            "missing_attribute",
            attribute=name,
            legacy_attribute=legacy_name,
            path=self._path,
        )
        return None

    def get_path(
        self,
        *args,
        default: Any = None,
    ) -> Any:
        yaml = self._yaml
        for arg in args:
            if not isinstance(yaml, dict):
                return default
            yaml = yaml.get(arg)
            if yaml is None:
                return default
        return yaml
