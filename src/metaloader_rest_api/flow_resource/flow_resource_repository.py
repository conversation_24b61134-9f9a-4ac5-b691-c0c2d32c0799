from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>e, text
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    StageBatchRepository,
)
from metaloader_rest_api.flow_param.flow_param_model import FlowParamDataType
from metaloader_rest_api.flow_resource.flow_resource_model import FlowResource

_DEFAULT_LOGGER = get_logger(__name__)


class FlowResourceRepository(BufferedStageBatchRepository[FlowResource]):
    def __init__(
        self,
        session: Session,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_flow_resource"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._module_id = module_id
        self._version_id = version_id
        self._effective_date = effective_date

    @property
    def module_id(self) -> int:
        return self._module_id

    @module_id.setter
    def module_id(self, value: int) -> None:
        self._module_id = value

    @property
    def version_id(self) -> int:
        return self._version_id

    @version_id.setter
    def version_id(self, value: int) -> None:
        self._version_id = value

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            return super().__exit__(exc_type, exc_value, traceback)
        except Exception as exception:
            exc_type = type(exception)
            raise exception
        finally:
            if exc_type is None:
                stage = self.reload_params()
                self.load_algorithm(stage)
                self.load_link(stage)

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                flow_rk                    BIGINT   NOT NULL
              , resource_cd                TEXT     NOT NULL
              , flow_resource_link_type_rk SMALLINT NOT NULL
              , etl_algorithm_cd           TEXT
              , resource_schema_name       TEXT
              , resource_table_name        TEXT
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (flow_rk
                               , resource_cd
                               , flow_resource_link_type_rk
                               , etl_algorithm_cd
                               , resource_schema_name
                               , resource_table_name)
            VALUES (:flow_id
                  , :resource_name
                  , :type
                  , :algorithm
                  , :resource_schema_name
                  , :resource_table_name)
        """

    def reload_params(self) -> str:
        log = self._log.bind(
            action="reload_params",
            table="mart_flow_resource_params",
        )
        log.info("begin", table_id=self._table_id)

        stage = f"stg.mart_flow_resource_params_{self._table_id}"

        log.info("create stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {stage} (
                    flow_rk                    BIGINT   NOT NULL
                  , resource_cd                TEXT     NOT NULL
                  , flow_resource_link_type_rk SMALLINT NOT NULL
                  , etl_algorithm_cd           TEXT
                  , resource_schema_name       TEXT
                  , resource_table_name        TEXT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {stage} (flow_rk
                                   , resource_cd
                                   , flow_resource_link_type_rk
                                   , etl_algorithm_cd
                                   , resource_schema_name
                                   , resource_table_name)
                       WITH p AS (SELECT *
                                    FROM (SELECT l.flow_rk
                                               , FORMAT('${{%s}}', b.flow_parameter_cd)                  flow_parameter_cd 
                                               , b.data_type_rk
                                               , COALESCE(l_p.flow_parameter_json, l.default_value_json) default_value_json
                                            FROM metamodel.link_flow_parameter                  l
                                            JOIN metamodel.bridge_flow_parameter                b
                                              ON b.flow_parameter_rk = l.flow_parameter_rk
                                             AND l.code_delivery_rk = :code_delivery_rk
                                             AND l.effective_to_dttm = :effective_to_dttm
                                             AND l.deleted_flg IS FALSE
                                       LEFT JOIN (SELECT
                                             DISTINCT ON (work_flow_rk
                                                        , flow_parameter_rk) 
                                                         *
                                                    FROM metamodel.link_flow_parameter_pass 
                                                   WHERE is_value_flg IS TRUE 
                                                     AND flow_parameter_json IS NOT NULL 
                                                     AND code_delivery_rk = :code_delivery_rk
                                                     AND effective_to_dttm = :effective_to_dttm
                                                     AND deleted_flg IS FALSE)                  l_p
                                              ON l_p.work_flow_rk      = l.flow_rk
                                             AND l_p.flow_parameter_rk = l.flow_parameter_rk) p
                                   WHERE default_value_json IS NOT NULL)
                     SELECT s.flow_rk                                                   flow_rk
                          , CASE
                            WHEN p_r.flow_rk IS NULL
                            THEN s.resource_cd
                            ELSE p_r_l.default_value_json
                             END resource_cd
                          , s.flow_resource_link_type_rk                                flow_resource_link_type_rk
                          , CASE
                            WHEN p_a.flow_rk IS NULL
                            THEN s.etl_algorithm_cd
                            ELSE p_a.default_value_json ->> 0
                             END                                                        etl_algorithm_cd
                          , CASE 
                            WHEN p_rs.default_value_json IS NOT NULL
                            THEN p_rs.default_value_json ->> 0
                            WHEN s.resource_schema_name  IS NULL
                             AND p_rt.default_value_json IS NOT NULL
                            THEN CASE p_rt.data_type_rk
                                 WHEN {FlowParamDataType.DICT}
                                 THEN p_rt.default_value_json ->> 'schema'
                                 ELSE SPLIT_PART(p_rt.default_value_json ->> 0, '.', 1)
                                  END
                            ELSE s.resource_schema_name
                             END                                                        resource_schema_name
                          , CASE 
                            WHEN p_rt.data_type_rk = {FlowParamDataType.DICT}
                            THEN p_rt.default_value_json ->> 'name'
                            WHEN p_rt.default_value_json IS NOT NULL
                            THEN CASE 
                                 WHEN s.resource_schema_name IS NOT NULL
                                 THEN p_rt.default_value_json ->> 0
                                 ELSE SPLIT_PART(p_rt.default_value_json ->> 0, '.', 2)
                                  END
                            ELSE s.resource_table_name
                             END                                                        resource_table_name
                       FROM (SELECT DISTINCT *
                               FROM {self._table})                          s
                  LEFT JOIN p                                               p_r
                         ON p_r.flow_rk            = s.flow_rk
                        AND p_r.flow_parameter_cd  = s.resource_cd
                  LEFT JOIN
                    LATERAL (SELECT JSONB_ARRAY_ELEMENTS_TEXT(p_r.default_value_json) default_value_json
                              WHERE p_r.data_type_rk = {FlowParamDataType.LIST}
                              UNION ALL
                             SELECT p_r.default_value_json ->> 0                      default_value_json
                              WHERE p_r.data_type_rk != {FlowParamDataType.LIST}) p_r_l
                         ON TRUE
                  LEFT JOIN p                                               p_a
                         ON p_a.flow_rk            = s.flow_rk
                        AND p_a.flow_parameter_cd  = s.etl_algorithm_cd
                  LEFT JOIN p                                               p_rs
                         ON p_rs.flow_rk           = s.flow_rk
                        AND p_rs.flow_parameter_cd = s.resource_schema_name
                  LEFT JOIN p                                               p_rt
                         ON p_rt.flow_rk           = s.flow_rk
                        AND p_rt.flow_parameter_cd = s.resource_table_name
                         
            """),
            params={
                "effective_to_dttm": LAST_DATE,
                "code_delivery_rk": self._module_id,
            },
        )

        return stage

    def load_algorithm(self, stage: str) -> None:
        log = self._log.bind(
            action="load_algorithm",
            table="bridge_etl_algorithm",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.bridge_etl_algorithm_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    etl_algorithm_cd TEXT
                  , code_delivery_rk SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (etl_algorithm_cd
                                          , code_delivery_rk)
                     SELECT DISTINCT
                            etl_algorithm_cd
                          , :code_delivery_rk code_delivery_rk
                       FROM {stage}
                      WHERE etl_algorithm_cd IS NOT NULL
                        AND etl_algorithm_cd NOT LIKE '%${{%}}%'
            """),
            params={
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.bridge_etl_algorithm",
            stage=merge_stage,
            primary_key="etl_algorithm_rk",
            keys=["etl_algorithm_cd"],
            values=[],
            others=["code_delivery_rk"],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_link(self, stage: str) -> None:
        log = self._log.bind(
            action="load_link",
            table="link_flow_resource",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_flow_resource_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    flow_rk                    BIGINT
                  , resource_rk                BIGINT
                  , flow_resource_link_type_rk SMALLINT
                  , etl_algorithm_rk           BIGINT
                  , table_rk                   BIGINT
                  , code_delivery_rk           SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (flow_rk
                                         , resource_rk
                                         , flow_resource_link_type_rk
                                         , etl_algorithm_rk
                                         , table_rk
                                         , code_delivery_rk)
                     SELECT DISTINCT
                            s.flow_rk                           flow_rk
                          , r.resource_rk                       resource_rk
                          , s.flow_resource_link_type_rk        flow_resource_link_type_rk
                          , COALESCE(ea.etl_algorithm_rk, -1)   etl_algorithm_rk
                          , CASE
                            WHEN s.resource_table_name  IS NULL
                             AND s.resource_schema_name IS NULL
                            THEN 0
                            ELSE COALESCE(t.table_rk, -1)
                             END                                table_rk
                          , :code_delivery_rk                   code_delivery_rk
                       FROM (SELECT DISTINCT *
                               FROM {stage})                          s
                       JOIN metamodel.bridge_resource                 r
                         ON r.resource_cd = s.resource_cd
                        AND r.effective_to_dttm = :effective_to_dttm
                        AND r.deleted_flg IS FALSE
                  LEFT JOIN metamodel.bridge_etl_algorithm            ea
                         ON ea.etl_algorithm_cd = s.etl_algorithm_cd
                        AND ea.code_delivery_rk = :code_delivery_rk
                        AND ea.effective_to_dttm = :effective_to_dttm
                        AND ea.deleted_flg IS FALSE
                  LEFT JOIN metamodel.bridge_table                    t
                         ON t.table_name  = s.resource_table_name
                        AND t.schema_name = s.resource_schema_name
                        AND t.effective_to_dttm = :effective_to_dttm
                        AND t.deleted_flg IS FALSE
            """),
            params={
                "effective_to_dttm": LAST_DATE,
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_flow_resource",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "flow_rk",
                "resource_rk",
                "flow_resource_link_type_rk",
                "etl_algorithm_rk",
                "table_rk",
            ],
            values=[],
            others=["code_delivery_rk"],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")
