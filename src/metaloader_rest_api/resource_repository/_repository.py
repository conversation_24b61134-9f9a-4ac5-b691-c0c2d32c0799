import json
from datetime import datetime
from typing import List, Literal, Optional, TypedDict, overload
from uuid import UUID

from sqlalchemy import Text<PERSON>lause, bindparam, text
from sqlalchemy.dialects.postgresql import JSONB, SMALLINT, TEXT
from sqlalchemy.orm import Session
from structlog.stdlib import get_logger

from metaloader_rest_api import models
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    ExecuteInSessionMixin,
)
from metaloader_rest_api.helpers import JsonDict, get_local_sql_file_reader
from metaloader_rest_api.loggable import Loggable
from metaloader_rest_api.models import ResourceAction

logger = get_logger(__name__)
_read_sql_file = get_local_sql_file_reader(__file__)

_TABLE_NAME_BRIDGE = "bridge_resource"


class StageResource(TypedDict):
    resource_cd: str
    action: int
    resource_type: str
    definition: Optional[JsonDict]


class ResourceStageBatchRepository(BufferedStageBatchRepository[StageResource]):
    def __init__(
        self,
        session: Session,
        table_id: str,
    ):
        super().__init__(
            session=session,
            table=_TABLE_NAME_BRIDGE,
            table_id=table_id,
            create_statement=self._create_statement,
            load_statement=self._load_statement,
        )

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        statement = statement.bindparams(
            bindparam("resource_cd", type_=TEXT),
            bindparam("action", type_=SMALLINT),
            bindparam("definition", type_=JSONB(none_as_null=True)),
        )
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                resource_cd         TEXT NOT NULL
              , data_action_type_rk SMALLINT NOT NULL
              , definition          JSONB
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (resource_cd
                               , data_action_type_rk
                               , definition)
            VALUES (:resource_cd
                  , :action
                  , :definition)
        """


class ResourceStageSequentialRepository(ExecuteInSessionMixin):
    def __init__(
        self,
        session: Session,
        load_id: UUID,
    ):
        self._session = session

        staging_table_name = self.get_staging_table_name(load_id)
        self._create_statement = ResourceStageBatchRepository._create_statement(
            staging_table_name
        )
        self._drop_statement = f"DROP TABLE IF EXISTS {staging_table_name}"
        self._insert_statement = ResourceStageBatchRepository._load_statement(
            staging_table_name
        )
        self._query_existing_actions_statement = f"""
            SELECT data_action_type_rk
            FROM {staging_table_name}
            WHERE resource_cd = :resource_cd
        """
        self._query_resources_statement = f"""
            SELECT resource_cd
            FROM {staging_table_name}
        """

    @classmethod
    def get_staging_table_name(cls, load_id: UUID) -> str:
        return f"stg.{_TABLE_NAME_BRIDGE}_{load_id.hex}"

    def create_table(self) -> None:
        self._execute(self._create_statement)

    def drop_table(self) -> None:
        self._execute(self._drop_statement)

    @overload
    def add_resource_action(
        self,
        resource_cd: str,
        action: Literal[models.ResourceAction.ADD, models.ResourceAction.MODIFY],
        definition: JsonDict,
    ) -> Optional[models.ResourceAction]: ...

    @overload
    def add_resource_action(
        self,
        resource_cd: str,
        action: Literal[models.ResourceAction.DELETE],
    ) -> Optional[models.ResourceAction]: ...

    def add_resource_action(
        self,
        resource_cd,
        action,
        definition=None,
    ):
        if action is not models.ResourceAction.DELETE and definition is None:
            raise ValueError("definition is required for every action except delete")

        if (
            existing_actions := self._execute(
                statement=self._query_existing_actions_statement,
                params={
                    "resource_cd": resource_cd,
                    "action": action.value,
                },
            )
            .scalars()
            .all()
        ):
            if len(existing_actions) > 1:
                logger.warning(
                    "Multiple actions found for resource",
                    resource_cd=resource_cd,
                    actions=existing_actions,
                )
            return models.ResourceAction(existing_actions[0])

        self._execute(
            statement=self._insert_statement,
            params={
                "resource_cd": resource_cd,
                "action": action.value,
                "definition": json.dumps(definition) if definition else None,
            },
        )

    def get_resource_cds(self) -> List[str]:
        # noinspection PyTypeChecker
        return (
            self._execute(
                statement=self._query_resources_statement,
            )
            .scalars()
            .all()
        )  # type: ignore


class ResourceRepository(ExecuteInSessionMixin, Loggable):
    _TABLE_BRIDGE_RESOURCE = "metamodel.bridge_resource"
    _TABLE_LINK_RESOURCE_SOURCE = "metamodel.link_resource_source"
    _TABLE_LINK_RESOURCE_TABLE = "metamodel.link_resource_table"
    _TABLE_BRIDGE_SOURCE = "metamodel.bridge_source"
    _TABLE_BRIDGE_TABLE = "metamodel.bridge_table"
    _SEQUENCE_METAMODEL_RK = "metamodel.md_seq"

    _STATEMENT_LOAD_RESOURCES = _read_sql_file("bridge")
    _STATEMENT_LOAD_SOURCE_LINKS = _read_sql_file("link_source")
    _STATEMENT_LOAD_TABLE_LINKS = _read_sql_file("link_table")

    def __init__(self, session: Session):
        self._log = logger.bind(action="load_resource")
        self._session = session
        self._deduped_stage_table = None

    def load(
        self,
        version_id: int,
        effective_date: datetime,
        stage_table: str,
    ) -> None:
        self._log = self._log.bind(
            version_id=version_id,
            effective_date=str(effective_date),
            stage_table=stage_table,
        )
        self._log.info("begin")

        self._ensure_staging_is_deduplicated(stage_table)
        self.load_resources(
            version_id,
            effective_date,
            stage_table,
        )
        self.load_source_links(
            effective_date,
            stage_table,
        )
        self.load_table_links(
            effective_date,
            stage_table,
        )

        self._log.info("end")

    def load_resources(
        self,
        version_id: int,
        effective_date: datetime,
        stage_table: str,
    ) -> None:
        log = self._log.bind(stage="resource")
        log.info("begin")

        self._ensure_staging_is_deduplicated(stage_table)
        self._execute(
            statement=self._STATEMENT_LOAD_RESOURCES.format(
                stage_table=self._deduped_stage_table,
                bridge_resource=self._TABLE_BRIDGE_RESOURCE,
                sequence_metamodel_rk=self._SEQUENCE_METAMODEL_RK,
            ),
            params={
                "version_rk": version_id,
                "effective_from_dttm": effective_date,
                "effective_to_dttm": LAST_DATE,
                "item_added": ResourceAction.ADD.value,
                "item_modified": ResourceAction.MODIFY.value,
                "item_deleted": ResourceAction.DELETE.value,
            },
        )
        log.info("end")

    def load_source_links(
        self,
        effective_date: datetime,
        stage_table: str,
    ) -> None:
        log = self._log.bind(stage="source_link")
        log.info("begin")

        self._ensure_staging_is_deduplicated(stage_table)
        self._execute(
            statement=self._STATEMENT_LOAD_SOURCE_LINKS.format(
                stage_table=self._deduped_stage_table,
                bridge_resource=self._TABLE_BRIDGE_RESOURCE,
                bridge_source=self._TABLE_BRIDGE_SOURCE,
                link_resource_source=self._TABLE_LINK_RESOURCE_SOURCE,
            ),
            params={
                "effective_from_dttm": effective_date,
                "effective_to_dttm": LAST_DATE,
                "item_added": ResourceAction.ADD.value,
                "item_modified": ResourceAction.MODIFY.value,
                "item_deleted": ResourceAction.DELETE.value,
            },
        )
        log.info("end")

    def load_table_links(
        self,
        effective_date: datetime,
        stage_table: str,
    ) -> None:
        log = self._log.bind(stage="table_link")
        log.info("begin")

        self._ensure_staging_is_deduplicated(stage_table)
        self._execute(
            statement=self._STATEMENT_LOAD_TABLE_LINKS.format(
                stage_table=self._deduped_stage_table,
                bridge_resource=self._TABLE_BRIDGE_RESOURCE,
                bridge_table=self._TABLE_BRIDGE_TABLE,
                link_resource_table=self._TABLE_LINK_RESOURCE_TABLE,
            ),
            params={
                "effective_from_dttm": effective_date,
                "effective_to_dttm": LAST_DATE,
                "item_added": ResourceAction.ADD.value,
                "item_modified": ResourceAction.MODIFY.value,
                "item_deleted": ResourceAction.DELETE.value,
            },
        )
        log.info("end")

    def _ensure_staging_is_deduplicated(self, stage_table: str) -> None:
        if self._deduped_stage_table:
            return

        new_name = self._deduplicate_staging(stage_table=stage_table)
        self._deduped_stage_table = new_name or stage_table

    def _deduplicate_staging(self, stage_table: str) -> Optional[str]:
        self._log.info("check_stage_table_duplicates")
        q = self._execute(
            statement=f"""
                    SELECT resource_cd
                    FROM {stage_table}
                    GROUP BY 1
                    HAVING count(*) > 1
                """
        )
        duplicate_resource_cds = q.scalars().all()
        if not duplicate_resource_cds:
            self._log.info("no_stage_table_duplicates")
            return None

        self._log.warning(
            "duplicate_resource_cds_in_stage",
            sample=duplicate_resource_cds[:5],
            count=len(duplicate_resource_cds),
        )
        deduped_stage_table = f"{stage_table}_deduped"
        self._log.info(
            "deduplicate_stage_table",
            deduped_stage_table=deduped_stage_table,
        )
        self._execute(
            # Не делаем ORDER BY, т.к. при загрузке через апи-транзакции отсутствие дубликатов
            # гарантирует код приложения, а при инит-загрузке дубли resource_cd (когда один и тот же
            # resource_cd есть более чем в одном провайдере) -- это дефект на стороне провайдеров,
            # который будут разрешать их разработчики.
            # Нам при этом нужно лишь не упасть и как-либо загрузить в метамодель.
            statement=f"""
                DROP TABLE IF EXISTS {deduped_stage_table};
                CREATE TABLE {deduped_stage_table} AS
                SELECT DISTINCT ON (resource_cd)
                    resource_cd
                  , data_action_type_rk
                  , definition
                FROM {stage_table}
            """,
        )
        return deduped_stage_table


_resource_exists_statement = text(
    """
    SELECT resource_rk
    FROM metamodel.bridge_resource
    WHERE resource_cd = :resource_cd
      AND effective_to_dttm = :effective_to_dttm
      AND deleted_flg IS FALSE
    """
)


def resource_exists(
    session: Session,
    resource_cd: str,
) -> bool:
    resource_rk = session.scalar(
        statement=_resource_exists_statement,
        params={
            "resource_cd": resource_cd,
            "effective_to_dttm": LAST_DATE,
        },
    )
    return resource_rk is not None


def count_staging_rows(session: Session, load_id: UUID) -> int:
    staging_table = ResourceStageSequentialRepository.get_staging_table_name(load_id)
    count_statement = text(f"SELECT COUNT(*) FROM {staging_table}")
    return session.scalar(count_statement) or 0
