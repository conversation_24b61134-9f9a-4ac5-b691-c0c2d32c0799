from collections.abc import Callable
from functools import partial
from typing import (
    Any,
    Iterator,
    Mapping,
    Optional,
    Protocol,
)

from metaloader_rest_api.flow_resource.flow_resource_model import (
    FLOW_RESOURCE_TABLE_DEFAULT,
    FlowResource,
    FlowResourceTable,
    flow_resource_factory,
    flow_resource_table,
    source_flow_resource_factory,
    target_flow_resource,
    target_flow_resource_factory,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit import (
    FlowResourceProcessorUnit,
)
from metaloader_rest_api.yaml.yaml_loader import Yaml
from metaloader_rest_api.yaml.yaml_wrapper import YamlWrapper


class _IdlFlowResourceProcessorUnitVisitor(Protocol):
    def __call__(
        self,
        self_: "IdlFlowResourceProcessorUnit",
        flow_id: int,
        name: str,
        properties: Yaml,
        **kwargs,
    ) -> Iterator[FlowResource]:
        pass


class IdlFlowResourceProcessorUnit(FlowResourceProcessorUnit):
    _Visitors = Mapping[str, _IdlFlowResourceProcessorUnitVisitor]

    _SCHEMA_VERSION = 2.0
    _SCHEMA_VERSIONS = set()

    _OPERATORS: _Visitors = {}

    _BUILDERS: _Visitors = {}

    _TEMPLATES: Mapping[str, _Visitors] = {}

    @classmethod
    def _init(cls) -> None:
        cls._SCHEMA_VERSIONS = {type_(cls._SCHEMA_VERSION) for type_ in (str, float)}

        cls._BUILDERS = {
            f"ceh_core_idl.app.builders.{name}": builder
            for name, builder in {
                "include_flow_builder": cls._include_flow_builder,
            }.items()
        }

        cls._OPERATORS = {
            f"ceh_core_idl.app.operators.services.{name}": operator
            for name, operator in {
                "reg_delta_operator": cls._reg_delta_operator,
                "multi_reg_delta_operator": cls._multi_reg_delta_operator,
                "get_max_loaded_version_operator": cls._get_max_loaded_version_operator,
            }.items()
        }

        cls._TEMPLATES = {
            "idl_cf_template": {
                "cf_ceh_template": cls._cf_ceh_template,
            },
            "ceh_frm_idl_templates_prototype": {
                "load_delta_template": cls._load_delta_template,
                "load_delta_bbridge_sal_template": cls._load_delta_bbridge_sal_template,
                "load_dummy_bbridge_template": cls._load_dummy_bbridge_template,
                "load_dummy_bbridge_sal_template": cls._load_dummy_bbridge_sal_template,
                "load_hub_template": cls._load_hub_template,
            },
            **{
                package: {"cf_uni_template": cls._cf_uni_template}
                for package in (
                    "rdv_cf_uni_template",
                    "rdv_cf_uni_template_computed_table",
                    "rdv_cf_uni_template_comp_table_proto_01",
                    "rdv_cf_uni_template_for_union_computed_table",
                    "rdv_cf_uni_template_old",
                    "rdv_cf_uni_template_proto_01",
                    "rdv_cf_uni_template_proto_02",
                    "rdv_cf_uni_dperiod_template",
                    "rdv_cf_uni_dperiod_template_ls",
                    "rdv_cf_uni_dperiod_template_ls_iview_rtll",
                    "cf_ext_trans_template",
                    "eod_template",
                )
            },
            **{
                package: {flow: cls._cf_uni_template}
                for package, flow in {
                    "rdv_cf_ods_template": "cf_ods_template",
                    "rdv_cf_uni_template_pk": "cf_uni_template_pk",
                    "rdv_cf_uni_template_kih": "cf_uni_template_kih",
                    "rdv_cf_uni_dperiod_template_ls": "cf_uni_last_template",
                    "rdv_cf_uni_dperiod_template_ls_iview_rtll": "cf_uni_last_template",
                    "rdv_cf_uni_dperiod_template_ls_iview_rtll_il": "cf_uni_last_template_il",
                    "rdv_cf_hist_incr_num_template": "cf_hist_incr_template",
                    "rdv_cf_snapshot_template_cftb": "cf_snapshot_template",
                    "rdv_cf_snapshot_template_zod": "cf_snapshot",
                }.items()
            },
            "rdv_cf_uni_template_v2": {
                "cf_uni_template": cls._cf_uni_template_resource,
            },
            "rdv_cf_snapshot_template": {
                "cf_snapshot": cls._cf_snapshot,
            },
        }

    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> Iterator[FlowResource]:
        if self._skip_rdv(flow):
            return

        flow = YamlWrapper(flow, log=self._log)

        if not flow.has_attributes():
            return

        self._check_schema(flow)

        imports = self._get_imports(flow)

        flows = flow.get("flows")
        if flows is None:
            return
        for flow_ in flows.as_iterator_with_id():
            steps, _ = flow_.get_with_legacy("steps", "tasks")
            if steps is None:
                continue
            for step in steps.as_iterator_with_id():
                for attribute, visitor in (
                    ("type", self._operator),
                    ("builder", self._builder),
                ):
                    name = step.get_attribute_with_default(attribute)
                    if name is None:
                        continue

                    yield from visitor(flow_id, name, step, imports)
                    break

    @staticmethod
    def _skip_rdv(
        flow: Yaml,
    ) -> bool:
        return flow.get("type") == "WORK_FLOW"

    def _check_schema(
        self,
        flow: YamlWrapper,
    ) -> None:
        schema_version = flow.get_attribute("schema_version")
        if schema_version is not None and schema_version not in self._SCHEMA_VERSIONS:
            self._log.warning(
                "schema_version",
                actual=schema_version,
                expected=self._SCHEMA_VERSION,
            )

    @staticmethod
    def _get_imports(flow: YamlWrapper) -> Mapping[str, str]:
        return {
            template: package
            for package, _, template in (
                import_.partition(".")
                for import_ in flow.get_path("metadata", "imports", default=())
            )
        }

    def _operator(
        self,
        flow_id: int,
        name: str,
        step: YamlWrapper,
        imports: Mapping[str, str],
    ) -> Iterator[FlowResource]:
        operator = self._OPERATORS.get(name)
        if operator is None:
            return

        properties = step.get("properties")
        if properties is None or not properties.has_attributes():
            return

        yield from operator(self, flow_id, name, properties)

    def _builder(
        self,
        flow_id: int,
        name: str,
        step: YamlWrapper,
        imports: Mapping[str, str],
    ) -> Iterator[FlowResource]:
        builder = self._BUILDERS.get(name)
        if builder is None:
            return

        properties = step.get("properties")
        if properties is None or not properties.has_attributes():
            return

        yield from builder(self, flow_id, name, properties, imports=imports)

    def _include_flow_builder(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
        imports: Mapping[str, str],
    ) -> Iterator[FlowResource]:
        ref = properties.get_attribute("ref")
        template = self._TEMPLATES.get(imports.get(ref), {}).get(ref)
        if template is None:
            return
        properties = properties.get("properties")
        if properties is None or not properties.has_attributes():
            return

        yield from template(self, flow_id, ref, properties)

    def _reg_delta_operator(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_flow_resource = self._reg_delta_operator_flow_target_resource_factory(
            flow_id, properties
        )
        if target_flow_resource is None:
            return

        target_dataset, target_dataset_legacy = properties.get_with_legacy(
            "target_dataset", "target_table_names"
        )
        if target_dataset is None:
            return

        if target_dataset_legacy:
            target_dataset_table = self._get_table_from_dataset(target_dataset)
        elif target_dataset.has_items():
            target_table_name = target_dataset.get_item(0)
            if target_table_name is None:
                target_dataset_table = FLOW_RESOURCE_TABLE_DEFAULT
            else:
                target_dataset_table = self._get_table_from_name_string(
                    target_table_name
                )
        else:
            target_dataset_table = None

        yield target_flow_resource(target_dataset_table)

    def _multi_reg_delta_operator(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_flow_resource = self._reg_delta_operator_flow_target_resource_factory(
            flow_id, properties
        )
        if target_flow_resource is None:
            return

        target_datasets, target_datasets_legacy = properties.get_with_legacy(
            "target_datasets", "target_table_desc"
        )
        if target_datasets is None:
            return
        if target_datasets_legacy:
            target_dataset_attribute = "target_dataset"
            target_dataset_visitor = self._get_table_from_dataset
        else:
            target_dataset_attribute = "table_name"
            target_dataset_visitor = self._get_table_from_name

        for target_dataset in target_datasets.as_iterator():
            target_dataset_ = target_dataset.get(target_dataset_attribute)
            if target_dataset_ is None:
                resource_table = FLOW_RESOURCE_TABLE_DEFAULT
            else:
                resource_table = target_dataset_visitor(target_dataset_)

            yield target_flow_resource(resource_table)

    @staticmethod
    def _reg_delta_operator_flow_target_resource_factory(
        flow_id: int,
        properties: YamlWrapper,
    ) -> Optional[Callable[[FlowResourceTable], FlowResource]]:
        target_resource_cd = properties.get_attribute_with_legacy(
            "target_resource_cd", "target_resource_name"
        )
        if target_resource_cd is None:
            return None

        algorithm_uid = properties.get_attribute_with_legacy(
            "algorithm_uid", "algorithm_name"
        )

        return partial(
            target_flow_resource_factory(algorithm_uid, flow_id),
            target_resource_cd,
        )

    def _get_max_loaded_version_operator(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        algorithm_uid = properties.get_attribute_with_legacy(
            "algorithm_uid", "algo_name"
        )

        target_flow_resource, source_flow_resource = flow_resource_factory(
            algorithm_uid,
            flow_id,
        )

        target_resource_cd = properties.get_attribute_with_legacy(
            "target_resource_cd", "target_resource_name"
        )
        if target_resource_cd is not None:
            yield target_flow_resource(target_resource_cd)

        source_resource_cds, _ = properties.get_with_legacy(
            "source_resource_cds", "source_resource_names"
        )
        if source_resource_cds is not None:
            for source_resource_cd in source_resource_cds.as_value_iterator():
                yield source_flow_resource(source_resource_cd)

    def _cf_ceh_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        algorithm_uid = properties.get_attribute("algorithm_uid")

        target_flow_resource, source_flow_resource = flow_resource_factory(
            algorithm_uid, flow_id
        )

        for attribute, resource in (
            ("target_resource_cds", target_flow_resource),
            ("source_resource_cds", source_flow_resource),
        ):
            resource_cds = properties.get(attribute)
            if resource_cds is None:
                continue
            for resource_cd in resource_cds.as_value_iterator():
                yield resource(resource_cd)

    def _load_delta_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_resource_name = properties.get_attribute("target_resource_name")
        if target_resource_name is None:
            return

        target_dataset = properties.get("target_dataset")
        if target_dataset is None:
            target_dataset_table = FLOW_RESOURCE_TABLE_DEFAULT
        else:
            target_dataset_table = self._get_table_from_dataset(target_dataset)

        algorithm_uid = properties.get_attribute("algorithm_uid")

        yield target_flow_resource(
            algorithm_uid,
            flow_id,
            target_resource_name,
            target_dataset_table,
        )

    def _load_delta_bbridge_sal_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        algorithm_uid = properties.get_attribute("algorithm_uid")

        target_flow_resource = target_flow_resource_factory(algorithm_uid, flow_id)

        for part in ("bbridge", "sal"):
            target_resource_name = properties.get_attribute(
                f"target_resource_name_{part}"
            )
            if target_resource_name is None:
                continue
            target_dataset = properties.get(f"target_dataset_{part}")
            if target_dataset is None:
                target_dataset_table = FLOW_RESOURCE_TABLE_DEFAULT
            else:
                target_dataset_table = self._get_table_from_dataset(target_dataset)
            yield target_flow_resource(target_resource_name, target_dataset_table)

    def _load_dummy_bbridge_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_resource_name = properties.get_attribute("target_resource_name")
        if target_resource_name is None:
            return

        target_object_prefix = properties.get_attribute_with_default(
            "target_object_prefix", "bbridge_"
        )

        target_object_name = properties.get_attribute("target_object_name")
        target_object_table = flow_resource_table(
            schema_name="idl",
            table_name=f"{target_object_prefix}{target_object_name}",
        )

        algorithm_uid = properties.get_attribute("algorithm_uid")

        yield target_flow_resource(
            algorithm_uid,
            flow_id,
            target_resource_name,
            target_object_table,
        )

    def _load_dummy_bbridge_sal_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_resource_names = properties.get("target_resource_names")
        if target_resource_names is None or not target_resource_names.has_items():
            return
        target_resource_name = target_resource_names.get_item(0)
        if target_resource_name is None:
            return

        algorithm_uid = properties.get_attribute("algorithm_uid")

        target_flow_resource = target_flow_resource_factory(algorithm_uid, flow_id)

        target_object_prefix = properties.get_attribute_with_default(
            "target_object_prefix", "bbridge_"
        )
        target_object_name = properties.get_attribute("target_object_name")

        for part in (target_object_prefix, "sal_"):
            yield target_flow_resource(
                target_resource_name,
                flow_resource_table(
                    schema_name="idl",
                    table_name=f"{part}{target_object_name}",
                ),
            )

    def _load_hub_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_resource_hub = properties.get_attribute("target_resource_hub")
        if target_resource_hub is None:
            return

        algorithm_uid = properties.get_attribute("algorithm_uid")

        target_dataset_hub = properties.get("target_dataset_hub")
        if target_dataset_hub is None:
            target_dataset_hub_table = FLOW_RESOURCE_TABLE_DEFAULT
        else:
            target_dataset_hub_table = self._get_table_from_dataset(target_dataset_hub)

        yield target_flow_resource(
            algorithm_uid,
            flow_id,
            target_resource_hub,
            target_dataset_hub_table,
        )

    def _cf_uni_template(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        algos_map = properties.get("algos_map")
        if algos_map is None:
            return

        for algorithm_uid, resources in algos_map.as_item_iterator():
            yield from self._cf_uni_template_resources(
                flow_id,
                algorithm_uid,
                resources,
            )

    def _cf_uni_template_resource(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        resources = properties.get("resources")
        if resources is None:
            return

        yield from self._cf_uni_template_resources(
            flow_id,
            None,
            resources,
        )

    def _cf_uni_template_resources(
        self,
        flow_id: int,
        algorithm_uid: Optional[str],
        resources: YamlWrapper,
    ) -> Iterator[FlowResource]:
        target_flow_resource, source_flow_resource = flow_resource_factory(
            algorithm_uid,
            flow_id,
        )
        for resource in resources.as_iterator():
            for attribute, resource_factory in (
                ("ceh", target_flow_resource),
                ("uni", source_flow_resource),
            ):
                if not resource.has_attributes():
                    continue
                resource_name = resource.get_attribute(attribute)
                if resource_name is None:
                    continue

                yield resource_factory(resource_name)

    def _cf_snapshot(
        self,
        flow_id: int,
        name: str,
        properties: YamlWrapper,
    ) -> Iterator[FlowResource]:
        algos_map = properties.get("algos_map")
        if algos_map is None:
            return

        for algorithm_uid, resources in algos_map.as_item_iterator():
            source_flow_resource = source_flow_resource_factory(algorithm_uid, flow_id)
            for resource in resources.as_iterator():
                resource_name = resource.as_string()
                if resource_name is None:
                    continue

                yield source_flow_resource(resource_name)

    @classmethod
    def _get_table_from_dataset(
        cls,
        dataset: YamlWrapper,
    ) -> FlowResourceTable:
        if dataset.has_attributes(quiet=True):
            return flow_resource_table(
                schema_name=dataset.get_attribute("schema"),
                table_name=dataset.get_attribute("name"),
            )
        else:
            return cls._get_table_from_name(dataset)

    @classmethod
    def _get_table_from_name(
        cls,
        name: YamlWrapper,
    ) -> FlowResourceTable:
        return cls._get_table_from_name_string(name.as_string())

    @classmethod
    def _get_table_from_name_string(
        cls,
        name: str,
    ) -> FlowResourceTable:
        if cls._is_param(name):
            return flow_resource_table(
                schema_name=None,
                table_name=name,
            )
        else:
            schema, _, table = name.partition(".")
            return flow_resource_table(schema, table)

    @classmethod
    def _is_param(
        cls,
        value: Any,
    ) -> bool:
        return isinstance(value, str) and "${" in value


IdlFlowResourceProcessorUnit._init()
