from enum import IntEnum
from typing import NamedTuple, Optional, Protocol, Tuple, TypedDict


class FlowResourceType(IntEnum):
    UNDEFINED = -1
    TARGET = 1
    SOURCE = 2


class FlowResource(TypedDict):
    type: FlowResourceType
    algorithm: Optional[str]
    flow_id: int
    resource_name: str
    resource_schema_name: Optional[str]
    resource_table_name: Optional[str]


class FlowResourceTable(NamedTuple):
    schema_name: Optional[str]
    table_name: Optional[str]


FLOW_RESOURCE_TABLE_DEFAULT = FlowResourceTable(None, None)


def source_flow_resource(
    algorithm: Optional[str],
    flow_id: int,
    resource_name: str,
) -> FlowResource:
    return flow_resource(
        type=FlowResourceType.SOURCE,
        algorithm=algorithm,
        flow_id=flow_id,
        resource_name=resource_name,
    )


def target_flow_resource(
    algorithm: Optional[str],
    flow_id: int,
    resource_name: str,
    resource_table: FlowResourceTable = FLOW_RESOURCE_TABLE_DEFAULT,
) -> FlowResource:
    return flow_resource(
        type=FlowResourceType.TARGET,
        algorithm=algorithm,
        flow_id=flow_id,
        resource_name=resource_name,
        resource_table=resource_table,
    )


def flow_resource(
    type: FlowResourceType,
    algorithm: Optional[str],
    flow_id: int,
    resource_name: str,
    resource_table: FlowResourceTable = FLOW_RESOURCE_TABLE_DEFAULT,
) -> FlowResource:
    return FlowResource(
        type=type,
        algorithm=algorithm,
        flow_id=flow_id,
        resource_name=resource_name,
        resource_schema_name=resource_table.schema_name,
        resource_table_name=resource_table.table_name,
    )


class SourceFlowResourceFactory(Protocol):
    def __call__(
        self,
        resource_name: str,
    ) -> FlowResource: ...


class TargetFlowResourceFactory(Protocol):
    def __call__(
        self,
        resource_name: str,
        resource_table: FlowResourceTable = FLOW_RESOURCE_TABLE_DEFAULT,
    ) -> FlowResource: ...


def flow_resource_factory(
    algorithm: Optional[str],
    flow_id: int,
) -> Tuple[TargetFlowResourceFactory, SourceFlowResourceFactory]:
    def source(
        resource_name: str,
    ) -> FlowResource:
        return source_flow_resource(
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resource_name,
        )

    def target(
        resource_name: str,
        resource_table: FlowResourceTable = FLOW_RESOURCE_TABLE_DEFAULT,
    ) -> FlowResource:
        return target_flow_resource(
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resource_name,
            resource_table=resource_table,
        )

    return target, source


def target_flow_resource_factory(
    algorithm: Optional[str],
    flow_id: int,
) -> TargetFlowResourceFactory:
    def target(
        resource_name: str,
        resource_table: FlowResourceTable = FLOW_RESOURCE_TABLE_DEFAULT,
    ) -> FlowResource:
        return target_flow_resource(
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resource_name,
            resource_table=resource_table,
        )

    return target


def source_flow_resource_factory(
    algorithm: Optional[str],
    flow_id: int,
) -> SourceFlowResourceFactory:
    def source(
        resource_name: str,
    ) -> FlowResource:
        return source_flow_resource(
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resource_name,
        )

    return source


def flow_resource_table(
    schema_name: Optional[str],
    table_name: Optional[str],
) -> FlowResourceTable:
    return FlowResourceTable(schema_name, table_name)
