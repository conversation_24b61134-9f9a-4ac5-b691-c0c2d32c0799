import io
import json
import time
from datetime import datetime
from pathlib import Path
from types import SimpleNamespace
from typing import Any, List, Optional, Sequence
from uuid import UUID

import kombu.exceptions
import prometheus_client
import pydantic
import structlog
from celery.result import AsyncResult
from fastapi import (
    BackgroundTasks,
    Body,
    Depends,
    FastAPI,
    File,
    HTTPException,
    Request,
    Response,
    UploadFile,
    status,
)
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from starlette.staticfiles import StaticFiles
from uvicorn.protocols.utils import get_path_with_query_string

from metaloader_rest_api import models, prometheus_metrics, schemas
from metaloader_rest_api.af_defaults import AfDefaults
from metaloader_rest_api.ceh_module import ceh_module_api
from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.celery_tasks.app_factory import get_celery_app
from metaloader_rest_api.common_param import DeploymentInfo
from metaloader_rest_api.data_model import data_model_api
from metaloader_rest_api.dev_kit import converter_excel
from metaloader_rest_api.dev_kit.converter_excel import LoadLogicalDataModelDemoFile
from metaloader_rest_api.flow_processor import flow_processor_api
from metaloader_rest_api.gzip.route import GzipRoute
from metaloader_rest_api.helpers import JsonDict
from metaloader_rest_api.loader_info_system.services.handler import (
    create_tx_for_load_system_info,
)
from metaloader_rest_api.logging import (
    get_metamodel_log_kwargs,
    pass_metamodel_log_kwargs,
    setup_logging,
)
from metaloader_rest_api.logic import (
    CELERY_QUEUE_PARALLEL,
    create_resource_actions_staging_table,
    create_tx,
    dry_run_release_sources_integration_etl,
    ensure_master_flow_valid,
    ensure_transaction_is_opened,
    execute_transactions_timeout,
    get_master_flow_names,
    get_master_flow_transaction_info_response,
    get_missing_master_flow_names,
    get_release_metadata,
    get_resource_transaction_info_response,
    initiate_loading_flows_from_airflow,
    initiate_transactions_statuses_check,
    query_transaction,
    query_transaction_master_flow_action,
    run_release_sources_integration_etl,
    send_task,
    update_transaction_instance_on_resolution,
)
from metaloader_rest_api.master_files_schemas import ControlFlow as MasterFlow
from metaloader_rest_api.master_flow_repository import find_master_flow
from metaloader_rest_api.models import SessionFactory, now_at_utc
from metaloader_rest_api.resource_repository import (
    ResourceStageSequentialRepository,
    resource_exists,
)
from metaloader_rest_api.resource_schemas import CehResource, UniResource
from metaloader_rest_api.settings import get_loggable_settings, read_settings

settings = read_settings()

setup_logging(log_format=settings.log_format, log_level=settings.log_level)
app_logger = structlog.stdlib.get_logger(__name__)
error_logger = structlog.stdlib.get_logger("api.error")
app_logger.info("Settings read", **get_loggable_settings(settings))
app = FastAPI(docs_url=None, redoc_url=None)
celery_client = get_celery_app(settings)
get_session = SessionFactory(database_url=settings.database_url.unicode_string())
app.mount(
    path="/static",
    app=StaticFiles(
        directory=str(Path(__file__).parent.resolve() / "static"),
    ),
    name="static",
)
app.mount("/metrics", prometheus_client.make_asgi_app())
app.router.route_class = GzipRoute  # noqa


# noinspection PyUnreachableCode
if True:  # TO-DO: поменять, когда будем готовы зарелизить аутентификацию
    # if settings.auth_disabled:
    auth = SimpleNamespace(
        with_roles=lambda *args, **kwargs: lambda: None,
    )
else:
    auth = Authorization(  # noqa
        oauth_token_url=str(settings.oauth_token_url),
        rs256_public_key=settings.rs256_public_key,
        public_roles=settings.public_roles.split(","),
    )
auth_with_public_role = auth.with_roles()
auth_with_modify_role = auth.with_roles("modify_role")


def yield_session():
    db = get_session()
    try:
        yield db
    finally:
        db.close()


@app.middleware("http")
async def logging_middleware(request: Request, call_next) -> Response:
    structlog.contextvars.clear_contextvars()

    start_time = time.perf_counter_ns()
    # If the call_next raises an error, we still want to return our own 500 response,
    # so we can add headers to it (process time, request ID...)
    response = Response(status_code=500)
    try:
        response = await call_next(request)
    except Exception:
        error_logger.exception("Uncaught exception")
        raise
    finally:
        process_time = time.perf_counter_ns() - start_time
        status_code = response.status_code
        url = get_path_with_query_string(request.scope)  # noqa
        client_host = request.client.host
        client_port = request.client.port
        http_method = request.method
        http_version = request.scope["http_version"]
        kwargs = {
            "http.url": str(request.url),
            "http.status_code": status_code,
            "http.method": http_method,
            "http.version": http_version,
            "tcp.remote.ip": client_host,
            "tcp.remote.port": client_port,
            "duration": process_time,
        }
        if auth_err_msg := getattr(request.state, "auth_err_msg", None):
            kwargs["http.auth_err_msg"] = auth_err_msg
        kwargs.update(get_metamodel_log_kwargs(request))
        app_logger.info(
            f"""{client_host}:{client_port} - "{http_method} {url} HTTP/{http_version}" {status_code}""",
            **kwargs,
        )
        return response


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    # noinspection PyUnresolvedReferences
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )


# noinspection PyUnresolvedReferences
@app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)
async def swagger_ui_redirect():
    return get_swagger_ui_oauth2_redirect_html()


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    # noinspection PyUnresolvedReferences
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=app.title + " - ReDoc",
        redoc_js_url="/static/redoc.standalone.js",
    )


@app.post(
    "/v1/tx",
    dependencies=[Depends(auth_with_modify_role)],
)
@app.post(
    "/v1/resources/tx",
    dependencies=[Depends(auth_with_modify_role)],
)
def create_transaction(
    background_tasks: BackgroundTasks,
    request: Request,
    release_metadata: schemas.ReleaseMetadata,
    timeout: int = Body(default=60 * 30, gte=0, lt=60 * 60 * 24),
    db: Session = Depends(yield_session),
) -> UUID:
    is_resource_tx = request.url.path.endswith("/resources/tx")

    model_class = (
        models.ResourceTransaction if is_resource_tx else models.MasterFlowTransaction
    )
    transaction = model_class(
        status=models.TransactionStatus.OPENED.value,
        timeout_sec=timeout,
        release_metadata=json.loads(
            release_metadata.model_dump_json()
        ),  # FIX-ME: optimize
    )
    db.add(transaction)
    db.commit()

    if is_resource_tx:
        create_resource_actions_staging_table(db, transaction.id)
        db.commit()

    pass_metamodel_log_kwargs(request, tx_uid=str(transaction.id))

    background_tasks.add_task(
        execute_transactions_timeout, get_session, app_logger, None
    )

    prometheus_metrics.set_non_resolved_transactions(db)

    return transaction.id


@app.post(
    "/v1/tx/{tx_uid}/rollback",
    dependencies=[Depends(auth_with_modify_role)],
)
@app.post(
    "/v1/resources/tx/{tx_uid}/rollback",
    dependencies=[Depends(auth_with_modify_role)],
)
def rollback_transaction(
    request: Request,
    tx_uid: UUID,
    db: Session = Depends(yield_session),
) -> None:
    pass_metamodel_log_kwargs(request, tx_uid=str(tx_uid))

    is_resource_tx = "/resources/tx/" in request.url.path
    model_class = (
        models.ResourceTransaction if is_resource_tx else models.MasterFlowTransaction
    )
    transaction = query_transaction(tx_uid, model_class, db, request)
    ensure_transaction_is_opened(transaction, request)

    # NOTE: функция изначально сделана только для MasterFlowTransaction, поэтому параметры
    # могут показаться странными, но для этого эндпойнта разницы до сих пор не было
    update_transaction_instance_on_resolution(
        transaction=transaction,
        status=schemas.TransactionStatus.ROLLEDBACK,
    )
    db.add(transaction)
    db.commit()


# noinspection DuplicatedCode
@app.post(
    "/v1/tx/{tx_uid}/commit",
    responses={
        status.HTTP_409_CONFLICT: {"model": schemas.TransactionResolutionErrorDetails},
    },
    dependencies=[Depends(auth_with_modify_role)],
)
def commit_master_flow_transaction(
    request: Request,
    tx_uid: UUID,
    mode: schemas.TransactionMode = Body(...),
    expected_master_flows: List[str] = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    """
    NOTE: сейчас этот эндпойнт подвержен race condition -- если такой запрос придёт одновременно
     на несколько воркеров uvicorn, то начнут выполнять эту функцию параллельно, и ETL запустится
     несколько раз. Статус IN_PROGRESS делает гонку чуть менее вероятной, но не избавляет от неё
     полностью.
     С появлением /commit_async и /try_async, эндпойнты /commit и /try можно считать deprecated.
    """
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        tx_mode=mode.value,
        n_expected_master_flows=str(len(expected_master_flows)),
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)
    if http_409 := _check_missing_master_flows(
        transaction, expected_master_flows, request
    ):
        return http_409

    # NOTE: если ETL выкинет исключение (чего он, впрочем, не должен делать в любом нормальном сценарии),
    # то приложение конечно ответит 500-й, но транзакция останется висеть в IN_PROGRESS до таймаута
    transaction.status = models.TransactionStatus.IN_PROGRESS.value
    db.add(transaction)
    db.commit()

    master_flow_errors = run_release_sources_integration_etl(
        session=db, tx_uid=tx_uid, integration_mode=mode
    )

    if master_flow_errors:
        db.rollback()

        update_transaction_instance_on_resolution(
            transaction=transaction,
            mode=mode,
            status=schemas.TransactionStatus.ETL_ERROR,
            expected_master_flows=expected_master_flows,
            master_flow_errors=master_flow_errors,
        )
        db.add(transaction)
        db.commit()

        err_msg = "failed to integrate some master flows, transaction rolled back"
        pass_metamodel_log_kwargs(request, error=err_msg)
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=schemas.TransactionResolutionErrorDetails(
                msg=err_msg,
                master_flows=master_flow_errors,
            ).model_dump(),
        )

    update_transaction_instance_on_resolution(
        transaction=transaction,
        mode=mode,
        status=schemas.TransactionStatus.COMMITTED,
        expected_master_flows=expected_master_flows,
    )
    db.add(transaction)
    db.commit()

    prometheus_metrics.set_transaction_master_flow_actions(db, tx_uid, "commit")


# noinspection DuplicatedCode
@app.post(
    "/v1/tx/{tx_uid}/try",
    responses={
        status.HTTP_409_CONFLICT: {"model": schemas.TransactionResolutionErrorDetails},
    },
    dependencies=[Depends(auth_with_modify_role)],
)
def try_master_flow_transaction(
    request: Request,
    tx_uid: UUID,
    mode: schemas.TransactionMode = Body(...),
    expected_master_flows: List[str] = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    """
    NOTE: см. коммент к /commit
    """
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        tx_mode=mode.value,
        n_expected_master_flows=str(len(expected_master_flows)),
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)
    if http_409 := _check_missing_master_flows(
        transaction, expected_master_flows, request
    ):
        return http_409

    transaction.status = models.TransactionStatus.IN_PROGRESS.value
    db.add(transaction)
    db.commit()

    master_flow_errors = dry_run_release_sources_integration_etl(
        session=db, tx_uid=tx_uid, integration_mode=mode
    )
    db.rollback()

    update_transaction_instance_on_resolution(
        transaction=transaction,
        mode=mode,
        status=(
            schemas.TransactionStatus.ETL_ERROR
            if master_flow_errors
            else schemas.TransactionStatus.ROLLEDBACK
        ),
        expected_master_flows=expected_master_flows,
        master_flow_errors=master_flow_errors,
    )
    db.add(transaction)
    db.commit()

    if master_flow_errors:
        err_msg = "failed to integrate some master flows, transaction rolled back"
        pass_metamodel_log_kwargs(request, error=err_msg)
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=schemas.TransactionResolutionErrorDetails(
                msg=err_msg,
                master_flows=master_flow_errors,
            ).model_dump(),
        )

    prometheus_metrics.set_transaction_master_flow_actions(db, tx_uid, "try")


_responses__process_master_flow_transaction_in_task_queue = {
    status.HTTP_202_ACCEPTED: {
        "description": "ETL scheduled, check trasaction status at the Location header"
    },
    status.HTTP_409_CONFLICT: {"model": schemas.TransactionResolutionErrorDetails},
}


@app.post(
    "/v1/tx/{tx_uid}/commit_async",
    responses=_responses__process_master_flow_transaction_in_task_queue,
)
@app.post(
    "/v1/tx/{tx_uid}/try_async",
    responses=_responses__process_master_flow_transaction_in_task_queue,
)
def process_master_flow_transaction_in_task_queue(
    request: Request,
    tx_uid: UUID,
    mode: schemas.TransactionMode = Body(...),
    expected_master_flows: List[str] = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    endpoint_called_is_commit = request.url.path.endswith("/commit_async")

    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        tx_mode=mode.value,
        n_expected_master_flows=str(len(expected_master_flows)),
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)
    if http_409 := _check_missing_master_flows(
        transaction, expected_master_flows, request
    ):
        return http_409

    # NOTE: Промежуточный статус у транзакции (между "opened" и "in_progress", например "queued")
    # выглядит полезным для пользователей апи, но с т.з. системы он не добавляет пользы, но зато
    # добавляет проблем. Если эта ручка успеет поменять статус, но селери-задача по какой-либо
    # причине не опубликуется, то заново позвать коммит для этой же транзакции уже не получится.
    # Эту проблему можно обойти паттерном "transactional outbox", но стоит ли?
    # https://microservices.io/patterns/data/transactional-outbox.html

    try:
        task_result: AsyncResult = celery_client.send_task(
            name=(
                "run_release_sources_integration_etl"
                if endpoint_called_is_commit
                else "dry_run_release_sources_integration_etl"
            ),
            kwargs=dict(
                tx_uid=str(tx_uid),
                integration_mode=mode.value,
            ),
            queue="sequential",
        )
        pass_metamodel_log_kwargs(
            request,
            celery_task_id=task_result.task_id,
        )
    except kombu.exceptions.OperationalError as err:
        pass_metamodel_log_kwargs(request, error=str(err))
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Failed to schedule task. Please try again later."},
        )

    prometheus_metrics.set_transaction_master_flow_actions(
        db,
        tx_uid,
        endpoint_name="commit_async" if endpoint_called_is_commit else "try_async",
    )

    return Response(
        status_code=status.HTTP_202_ACCEPTED,
        headers={"Location": f"/v1/tx/{tx_uid}"},
    )


@app.post(
    "/v1/resources/tx/{tx_uid}/commit",
    dependencies=[Depends(auth_with_modify_role)],
)
def commit_resource_transaction(
    request: Request,
    tx_uid: UUID,
    mode: schemas.TransactionMode = Body(..., embed=True),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        tx_mode=mode.value,
    )

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    try:
        task_result: AsyncResult = celery_client.send_task(
            name="run_resources_integration_etl",
            kwargs=dict(
                tx_uid=str(tx_uid),
            ),
            queue="sequential",
        )
        pass_metamodel_log_kwargs(
            request,
            celery_task_id=task_result.task_id,
        )
    except kombu.exceptions.OperationalError as err:
        pass_metamodel_log_kwargs(request, error=str(err))
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Failed to schedule task. Please try again later."},
        )

    prometheus_metrics.set_transaction_resource_actions(
        db, tx_uid, endpoint_name="commit"
    )

    return Response(
        status_code=status.HTTP_202_ACCEPTED,
        headers={"Location": f"/v1/resources/tx/{tx_uid}"},
    )


@app.get(
    "/v1/tx/{tx_uid}",
    dependencies=[Depends(auth_with_public_role)],
)
def get_master_flow_transaction_info(
    request: Request,
    tx_uid: UUID,
    db: Session = Depends(yield_session),
) -> schemas.TransactionInfo:
    pass_metamodel_log_kwargs(request, tx_uid=str(tx_uid))
    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    transaction_info = get_master_flow_transaction_info_response(transaction)
    return transaction_info


@app.get(
    "/v1/resources/tx/{tx_uid}",
    dependencies=[Depends(auth_with_public_role)],
)
def get_resource_transaction_info(
    request: Request,
    tx_uid: UUID,
    db: Session = Depends(yield_session),
) -> schemas.ResourceTransactionInfo:
    pass_metamodel_log_kwargs(request, tx_uid=str(tx_uid))
    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    transaction_info = get_resource_transaction_info_response(db, transaction)
    return transaction_info


_responses__create_master_flow_action = {
    status.HTTP_409_CONFLICT: {"model": schemas.MasterFlowActionErrorDetails},
}


@app.get(
    "/v1/releases/{module}",
    dependencies=[Depends(auth_with_public_role)],
)
def get_release(
    module: str,
    db: Session = Depends(yield_session),
) -> schemas.ReleaseMetadata:
    return get_release_metadata(db, module)


@app.get(
    "/v1/master_flows/names/{module}",
    dependencies=[Depends(auth_with_public_role)],
)
def get_master_flows_names(
    module: str,
    db: Session = Depends(yield_session),
) -> Sequence[str]:
    return get_master_flow_names(db, module)


@app.post(
    "/v1/master_flows/actions/added",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_master_flow_action,
    dependencies=[Depends(auth_with_modify_role)],
)
def create_master_flow_action_added(
    request: Request,
    tx_uid: UUID = Body(...),
    name: str = Body(..., min_length=1),
    definition: MasterFlow = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        masterflow_name=name,
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)
    if existing_action := query_transaction_master_flow_action(name, transaction, db):
        return _409_master_flow_action_already_exists(name, existing_action)

    release_metadata = schemas.ReleaseMetadata(**transaction.release_metadata)
    module = release_metadata.release_module

    if find_master_flow(db, module, name):
        pass_metamodel_log_kwargs(request, warn="master flow already exists")
        app_logger.warn(
            "master_flow_collision", action="add", master_flow=name, tx_uid=str(tx_uid)
        )

    master_flow_action = models.MasterFlowTransactionItem(
        name=name,
        content_json=definition.model_dump(mode="json"),
        action=models.MasterFlowAction.ADD.value,
        transaction_id=transaction.id,
    )
    db.add(master_flow_action)
    db.commit()

    ensure_master_flow_valid(definition, request)


@app.post(
    "/v1/master_flows/actions/modified",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_master_flow_action,
    dependencies=[Depends(auth_with_modify_role)],
)
def create_master_flow_action_modified(
    request: Request,
    tx_uid: UUID = Body(...),
    name: str = Body(..., min_length=1),
    definition: MasterFlow = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        masterflow_name=name,
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)
    if existing_action := query_transaction_master_flow_action(name, transaction, db):
        return _409_master_flow_action_already_exists(name, existing_action)

    release_metadata = schemas.ReleaseMetadata(**transaction.release_metadata)
    module = release_metadata.release_module

    if not find_master_flow(db, module, name):
        pass_metamodel_log_kwargs(request, warn="master flow doesn't exist")
        app_logger.warn(
            "master_flow_collision",
            action="change",
            master_flow=name,
            tx_uid=str(tx_uid),
        )

    master_flow_action = models.MasterFlowTransactionItem(
        name=name,
        content_json=definition.model_dump(mode="json"),
        action=models.MasterFlowAction.MODIFY.value,
        transaction_id=transaction.id,
    )
    db.add(master_flow_action)
    db.commit()

    ensure_master_flow_valid(definition, request)


@app.post(
    "/v1/master_flows/actions/renamed",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_master_flow_action,
    dependencies=[Depends(auth_with_modify_role)],
)
def create_master_flow_action_renamed(
    request: Request,
    tx_uid: UUID = Body(...),
    name: str = Body(..., min_length=1),
    new_name: str = Body(..., min_length=1),
    definition: Optional[MasterFlow] = Body(default=None),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        masterflow_name=name,
        masterflow_new_name=new_name,
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)
    if existing_action := query_transaction_master_flow_action(name, transaction, db):
        return _409_master_flow_action_already_exists(name, existing_action)

    release_metadata = schemas.ReleaseMetadata(**transaction.release_metadata)
    module = release_metadata.release_module

    if not find_master_flow(db, module, name):
        pass_metamodel_log_kwargs(request, warn="master flow doesn't exist")
        app_logger.warn(
            "master_flow_collision",
            action="rename",
            master_flow=name,
            tx_uid=str(tx_uid),
        )

    master_flow_action = models.MasterFlowTransactionItem(
        name=new_name,
        old_name=name,
        content_json=definition.model_dump(mode="json") if definition else None,
        action=models.MasterFlowAction.RENAME.value,
        transaction_id=transaction.id,
    )
    db.add(master_flow_action)
    db.commit()

    ensure_master_flow_valid(definition, request)


@app.post(
    "/v1/master_flows/actions/deleted",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_master_flow_action,
    dependencies=[Depends(auth_with_modify_role)],
)
def create_master_flow_action_deleted(
    request: Request,
    tx_uid: UUID = Body(...),
    name: str = Body(..., min_length=1),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        masterflow_name=name,
    )

    transaction = query_transaction(tx_uid, models.MasterFlowTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    if existing_action := query_transaction_master_flow_action(name, transaction, db):
        return _409_master_flow_action_already_exists(name, existing_action)

    release_metadata = schemas.ReleaseMetadata(**transaction.release_metadata)
    module = release_metadata.release_module

    if not find_master_flow(db, module, name):
        pass_metamodel_log_kwargs(request, warn="master flow doesn't exist")
        app_logger.warn(
            "master_flow_collision",
            action="delete",
            master_flow=name,
            tx_uid=str(tx_uid),
        )

    master_flow_action = models.MasterFlowTransactionItem(
        name=name,
        action=models.MasterFlowAction.DELETE.value,
        transaction_id=transaction.id,
    )
    db.add(master_flow_action)
    db.commit()


_responses__create_resource_action = {
    status.HTTP_409_CONFLICT: {"model": schemas.ResourceActionErrorDetails},
}
_description__create_resource_action = (
    "В рамках одной транзакции можно добавить только одно действие для каждого "
    "уникального resource_cd.\n"
    "Ожидается, что resource_cd в json-определении и в теле запроса совпадают. В противном случае "
    "при загрузке в метамодель будет использовано значение из json-определения."
)


@app.post(
    "/v1/resources/actions/added",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_resource_action,
    dependencies=[Depends(auth_with_modify_role)],
    description=_description__create_resource_action,
)
@app.post(
    "/v1/resources/actions/modified",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_resource_action,
    dependencies=[Depends(auth_with_modify_role)],
    description=_description__create_resource_action,
)
def create_resource_action_added_or_modified(
    request: Request,
    tx_uid: UUID = Body(...),
    resource_cd: str = Body(..., min_length=1),
    resource_type: schemas.ResourceType = Body(...),
    definition: JsonDict = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        resource_cd=resource_cd,
        resource_type=resource_type.value,
    )

    action_type = (
        models.ResourceAction.ADD
        if request.url.path.endswith("/added")
        else models.ResourceAction.MODIFY
    )
    resource_schema_cls = {
        schemas.ResourceType.CEH: CehResource,
        schemas.ResourceType.UNI: UniResource,
    }[resource_type]

    try:
        resource_schema_cls(**definition)
    except pydantic.ValidationError as err:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=err.errors(),
        )

    payload_resource_cd = definition.get("resource_cd")
    if payload_resource_cd and payload_resource_cd != resource_cd:
        app_logger.warning(
            "resource_cd in the json definition and in the body don't match",
            body_resource_cd=resource_cd,
            json_resource_cd=payload_resource_cd,
        )
    resource_cd = payload_resource_cd

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    resource_exists_ = resource_exists(db, resource_cd)
    if action_type is models.ResourceAction.ADD and resource_exists_:
        pass_metamodel_log_kwargs(request, warn="resource already exists")
    elif action_type is models.ResourceAction.MODIFY and not resource_exists_:
        pass_metamodel_log_kwargs(request, warn="resource doesn't exist")

    repo = ResourceStageSequentialRepository(session=db, load_id=transaction.id)
    existing_action = repo.add_resource_action(
        resource_cd=resource_cd,
        action=action_type,
        definition=definition,
    )
    db.commit()
    if existing_action:
        return _409_resource_action_already_exists(resource_cd, existing_action)


@app.post(
    "/v1/resources/actions/deleted",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_resource_action,
    dependencies=[Depends(auth_with_modify_role)],
    description=_description__create_resource_action,
)
def create_resource_action_deleted(
    request: Request,
    tx_uid: UUID = Body(...),
    resource_cd: str = Body(..., min_length=1),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        resource_cd=resource_cd,
    )

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    if not resource_exists(db, resource_cd):
        pass_metamodel_log_kwargs(request, warn="resource doesn't exist")

    repo = ResourceStageSequentialRepository(session=db, load_id=transaction.id)
    existing_action = repo.add_resource_action(
        resource_cd=resource_cd,
        action=models.ResourceAction.DELETE,
    )
    db.commit()
    if existing_action:
        return _409_resource_action_already_exists(resource_cd, existing_action)


@app.post(
    "/v1/flows/load_from_af",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_flows_from_af(
    module: str = Body(...),
    release: str = Body(pattern="^\d+\.\d+\.\d+$"),
    af_url: str = Body(...),
    ceh_etl_src_url: str = Body(default=None, deprecated=True),
    page_size: int = Body(default=AfDefaults.DAG_PAGE_SIZE.value),
    keep_dag: bool = Body(default=True),
    with_tasks: bool = Body(default=True),
    parse_master_flows: bool = Body(default=True),
    limit: int = Body(default=AfDefaults.DAG_LIMIT.value),
    webhook: Optional[str] = Body(default=None),
    yaml_loader: Optional[str] = Body(default=None, deprecated=True),
    scan_method: Optional[str] = Body(default=None, deprecated=True),
    fail_threshold: Optional[int] = Body(default=None, deprecated=True),
    db_session: Session = Depends(yield_session),
) -> UUID:
    tx_id = initiate_loading_flows_from_airflow(
        celery_client=celery_client,
        db_session=db_session,
        logger=app_logger,
        module=module,
        version=release,
        af_url=af_url,
        page_size=page_size,
        keep_dag=keep_dag,
        with_tasks=with_tasks,
        parse_master_flows=parse_master_flows,
        limit=limit,
        webhook=webhook,
    )

    if ceh_etl_src_url:
        process_flows_params = flow_processor_api.ProcessFlowsParams(
            deployment=DeploymentInfo(
                module=module,
                release=release,
            ),
            ceh_etl_src_url=ceh_etl_src_url,
            processors=[
                "idl_parameters",
                "idl_resources",
            ],
        )
        flows_parameters_tx_id = send_task(
            "process_flows",
            {"params": process_flows_params},
            CELERY_QUEUE_PARALLEL,
            celery_client,
            db_session,
            app_logger,
        )

        txs_status_checker_tx_id = initiate_transactions_statuses_check(
            celery_client=celery_client,
            db_session=db_session,
            transactions_ids=(tx_id, flows_parameters_tx_id),
            logger=app_logger,
        )

        return txs_status_checker_tx_id

    return tx_id


@app.post(
    "/v1/resources/load_from_providers",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_resources_from_providers(
    module: str = Body(...),
    release: str = Body(..., pattern=r"^\d+\.\d+\.\d+$"),
    uni_provider_base_url: str = Body(...),
    ceh_provider_base_url: str = Body(...),
    limit: Optional[int] = Body(None),
    webhook: Optional[str] = Body(None),
    db_session: Session = Depends(yield_session),
) -> UUID:
    tx_id = send_task(
        "load_resources_from_providers",
        {
            "module": module,
            "version": release,
            "uni_provider_base_url": uni_provider_base_url,
            "ceh_provider_base_url": ceh_provider_base_url,
            "limit": limit,
            "webhook": webhook,
        },
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )

    return tx_id


@app.post(
    "/v1/flows/process",
    dependencies=[Depends(auth_with_modify_role)],
)
def process_flows(
    params: flow_processor_api.ProcessFlowsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "process_flows",
        {"params": params},
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/sources/load_from_xlsx",
    summary="Загрузка шаблона stg_bridge_source",
    description="""
    Выполняет загрузку информации по системам и подсистемам
    """,
    dependencies=[Depends(auth_with_modify_role)],
)
def load_sources_from_xlsx(
    xlsx_file: UploadFile = File(
        ...,
        title="Обязательный xlsx",
        description="Ожидается шаблон stg_bridge_source",
    ),
    db: Session = Depends(yield_session),
    effective_from_dttm: Optional[datetime] = Body(default=""),
    background_tasks: BackgroundTasks = None,
) -> UUID:
    tx_id, task, task_args = create_tx_for_load_system_info(
        db,
        xlsx_file.filename,
        xlsx_file.file.read(),
    )

    if effective_from_dttm == "" or effective_from_dttm is None:
        effective_from_dttm = now_at_utc()

    task_args.append(effective_from_dttm)

    background_tasks.add_task(task, *task_args)

    return tx_id


@app.post(
    "/v1/module/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_modules(
    params: ceh_module_api.LoadModulesParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_modules",
        {"params": params},
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/data_model/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_data_model(
    params: data_model_api.LoadDataModelParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_data_model",
        {"params": params},
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/data_model/load_logical",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_logical_data_model(
    params: data_model_api.LoadLogicalDataModelParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_logical_data_model",
        {"params": params},
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/demo/data_model/excel_to_json",
    dependencies=[Depends(auth_with_modify_role)],
)
async def load_logical_data_model_demo(
    xlsx_file: UploadFile = File(
        ...,
    ),
    data_layer: str = Body(...),
    db_session: Session = Depends(yield_session),
) -> UUID:
    file_content = await xlsx_file.read()
    file_io = io.BytesIO(file_content)

    params = LoadLogicalDataModelDemoFile(
        data_layer=data_layer, file_name=xlsx_file.filename, file_io=file_io
    )

    load_id, effective_date = create_tx(db_session, app_logger)
    log = app_logger.bind(load_id=load_id, effective_date=effective_date)

    json_data = converter_excel.transform_excel_to_json(
        params,
        log,
    )

    data_model_api.load_logical_data_model(
        db_session,
        load_id,
        effective_date,
        json_data,
        log,
    )

    return load_id


@app.post(
    "/v1/service/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_services(
    params: ceh_service_api.LoadServicesParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_services",
        {"params": params},
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


def _409_master_flow_action_already_exists(
    name: str, existing_action: schemas.MasterFlowAction
) -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content=schemas.MasterFlowActionErrorDetails(
            msg="the transaction already has an action for this master flow",
            name=name,
            existing_action=existing_action,
        ).model_dump(mode="json", exclude_unset=True),
    )


def _409_resource_action_already_exists(
    resource_cd: str, existing_action: models.ResourceAction
) -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content=schemas.ResourceActionErrorDetails(
            msg="the transaction already has an action for this resource",
            resource_cd=resource_cd,
            existing_action={
                models.ResourceAction.ADD: schemas.ResourceAction.ADD,
                models.ResourceAction.MODIFY: schemas.ResourceAction.MODIFY,
                models.ResourceAction.DELETE: schemas.ResourceAction.DELETE,
            }[existing_action],
        ).model_dump(mode="json", exclude_unset=True),
    )


def _check_missing_master_flows(
    transaction: models.MasterFlowTransaction,
    expected_master_flows: List[str],
    request: Request,
) -> Optional[JSONResponse]:
    missing_master_flows = get_missing_master_flow_names(
        transaction, expected_master_flows
    )
    if not missing_master_flows:
        return None

    err_msg = "transaction is missing the expected master flows"
    pass_metamodel_log_kwargs(request, error=err_msg)
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content=schemas.TransactionResolutionErrorDetails(
            msg=err_msg,
            master_flows={name: ["missing"] for name in missing_master_flows},
        ).model_dump(),
    )
