import tempfile
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional, Tuple

from metaloader_rest_api.file_provider.file_provider import FileProvider
from metaloader_rest_api.flow_resource.flow_resource_model import (
    FlowResource,
    FlowResourceType,
    flow_resource,
    flow_resource_table,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit_idl import (
    IdlFlowResourceProcessorUnit,
)
from metaloader_rest_api.local_file_profider.local_file_provider import (
    LocalFileProvider,
)
from metaloader_rest_api.yaml.yaml_loader import Yaml
from pytest import fixture, mark, param


@dataclass
class FlowTestCaseInput:
    operator: str

    source_resources: List[str] = field(default_factory=list)
    target_resources: List[str] = field(default_factory=list)

    source_resource_schemas: List[Optional[str]] = field(default_factory=list)
    source_resource_tables: List[Optional[str]] = field(default_factory=list)

    target_resource_schemas: List[Optional[str]] = field(default_factory=list)
    target_resource_tables: List[Optional[str]] = field(default_factory=list)

    flow_id: int = 52
    algorithm: str = "test_alg_test_table_52"
    flow_name: str = "flow_test"
    algorithm_field: str = "algorithm_uid"

    operator_field: str = "type"
    target_resource_field: str = "target_resource_name"
    source_resource_field: str = "source_resource_names"

    # source_resource_names: Optional[List[str]] = None
    target_table_names: Optional[List[str]] = None


@mark.parametrize(
    "flow_case",
    [
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.reg_delta_operator",
                target_resources=["ceh.test_schema_1.test_table_1.test_cd"],
                target_resource_schemas=["test_schema_1"],
                target_resource_tables=["test_table_1"],
                target_table_names=["test_schema_1.test_table_1"],
            ),
            id="with reg delta operator and target table names",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.reg_delta_operator",
                target_resources=["ceh.test_schema_2.test_table_2"],
                target_resource_schemas=["test_schema_2"],
                target_resource_tables=["test_table_2"],
            ),
            id="with reg delta operator and target dataset",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.reg_delta_operator",
                target_resources=["ceh.test_schema_3.test_table_3.test_cd"],
                target_resource_schemas=["test_schema_3"],
                target_resource_tables=["test_table_3"],
                target_table_names=["test_schema_3.test_table_3"],
                target_resource_field="target_resource_cd",
            ),
            id="with reg delta operator and target table cd",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.reg_delta_operator",
                target_resources=["ceh.test_schema_4.test_table_4"],
                target_resource_schemas=["test_schema_4"],
                target_resource_tables=["test_table_4"],
                target_table_names=[
                    "test_schema_4.test_table_4",
                    "test_schema_4.test_table_6",
                ],
            ),
            id="with reg delta operator and multiple target table names",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.reg_delta_operator",
                target_resources=["ceh.test_schema_5.test_table_5"],
                target_resource_schemas=["test_schema_5"],
                target_resource_tables=["test_table_5"],
                target_table_names=["test_schema_5.test_table_5"],
                algorithm_field="algorithm_name",
            ),
            id="with reg delta operator and algorithm uid",
        ),
        # add params
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.multi_reg_delta_operator",
                target_resources=["ceh.test_schema_6.test_table_6"],
                target_resource_schemas=["test_schema_6"],
                target_resource_tables=["test_table_6"],
                target_table_names=["test_schema_6.test_table_6"],
            ),
            id="with multi reg delta operator and one target_table_desc",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.multi_reg_delta_operator",
                target_resources=["ceh.test_schema_7.test_table_7"],
                target_resource_schemas=["test_schema_7", "test_schema_7"],
                target_resource_tables=["test_table_7", "test_table_8"],
                target_table_names=[
                    "test_schema_7.test_table_7",
                    "test_schema_7.test_table_8",
                ],
            ),
            id="with multi reg delta operator and multiple target_table_desc",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.multi_reg_delta_operator",
                target_resources=["ceh.test_schema_8.test_table_8"],
                target_resource_schemas=["test_schema_8"],
                target_resource_tables=["test_table_8"],
            ),
            id="with multi reg delta operator and one target_dataset",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.multi_reg_delta_operator",
                target_resources=["ceh.test_schema_9.test_table_9"],
                target_resource_schemas=["test_schema_9", "test_table_9"],
                target_resource_tables=["test_schema_9", "test_table_10"],
            ),
            id="with multi reg delta operator and multiple target_datasets",
        ),
        # add params
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.get_max_loaded_version_operator",
                target_resources=["ceh.test_20.table_1.test_cd"],
                source_resources=["ceh.test_20.table_2.test_cd"],
                algorithm_field="algo_name",
                target_resource_field="target_resource_cd",
                source_resource_field="source_resource_cds",
            ),
            id="with get max loaded version and target_resource_cd + one cds",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.get_max_loaded_version_operator",
                target_resources=["ceh.test_21.table_1.test_cd"],
                source_resources=[
                    "ceh.test_21.table_2.test_cd",
                    "ceh.test_21.table_3.test_cd",
                ],
                algorithm_field="algo_name",
                target_resource_field="target_resource_cd",
                source_resource_field="source_resource_cds",
            ),
            id="with get max loaded version and target_resource_cd + many cds",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.get_max_loaded_version_operator",
                algorithm="${algorithm_uid}",
                target_resources=["${target_resource_param}"],
                source_resources=["${source_resource_param}"],
                algorithm_field="algo_name",
                target_resource_field="target_resource_cd",
                source_resource_field="source_resource_cds",
            ),
            id="with get max loaded version and target_resource_cd + param cds",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.get_max_loaded_version_operator",
                target_resources=["ceh.test_24.table_1"],
                source_resources=["ceh.test_23.table_2"],
                target_resource_field="target_resource_name",
                source_resource_field="source_resource_names",
            ),
            id="with get max loaded version and target_resource_name + one names",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.get_max_loaded_version_operator",
                target_resources=["ceh.test_24.table_1"],
                source_resources=["ceh.test_24.table_2", "ceh.test_24.table_3"],
                target_resource_field="target_resource_name",
                source_resource_field="source_resource_names",
            ),
            id="with get max loaded version and target_resource_name + many names",
        ),
        param(
            FlowTestCaseInput(
                operator="ceh_core_idl.app.operators.services.get_max_loaded_version_operator",
                algorithm="${algorithm_uid}",
                target_resources=["ceh.test_25.table_1.test_cd"],
                source_resources=["${source_resource_param}"],
                target_resource_field="target_resource_cd",
                source_resource_field="source_resource_names",
            ),
            id="with get max loaded version and target_resource_cd + param names",
        ),
        # param(
        #     FlowTestCaseInput(
        #         operator="cf_ceh_template",
        #         target_resources=["ceh.test_1.target.test_cd"],
        #         source_resources=["ceh.test_1.source.test_cd"],
        #         operator_field="ref",
        #         algorithm_field="algorithm_uid",
        #         target_resource_field="target_resource_cds",
        #         source_resource_field="source_resource_cds",
        #     ),
        #     id="cf_ceh with single target and single source"
        # ),
    ],
)
def test_process_parametrized(
    flow_case: FlowTestCaseInput, idl_flow_resource_processor
):
    yaml, expected = generate_flow_case(flow_case)
    print(yaml)
    actual = list(
        idl_flow_resource_processor.process(
            flow_case.flow_id, flow_case.flow_name, yaml
        )
    )
    assert expected == actual


@fixture(scope="module")
def abstract_file_provider() -> FileProvider:
    return LocalFileProvider(path=Path(tempfile.gettempdir()))


@fixture(scope="function")
def idl_flow_resource_processor(abstract_file_provider: FileProvider):
    return IdlFlowResourceProcessorUnit(abstract_file_provider)


def generate_flow_case(case: FlowTestCaseInput) -> Tuple[Yaml, List[FlowResource]]:
    flow_id = case.flow_id

    props = {
        case.algorithm_field: case.algorithm,
    }

    # Установка target_resource_field
    if case.target_resources:
        props[case.target_resource_field] = (
            case.target_resources[0]
            if len(case.target_resources) == 1
            else case.target_resources
        )

    # Установка source_resource_field
    if case.source_resources:
        props[case.source_resource_field] = (
            case.source_resources[0]
            if len(case.source_resources) == 1
            else case.source_resources
        )

    if case.operator == "ceh_core_idl.app.operators.services.reg_delta_operator":
        if case.target_table_names:
            props["target_table_names"] = case.target_table_names
        else:
            props["target_dataset"] = {
                "schema": case.target_resource_schemas[0],
                "name": case.target_resource_tables[0],
            }

    elif (
        case.operator == "ceh_core_idl.app.operators.services.multi_reg_delta_operator"
    ):
        if case.target_table_names:
            props["target_table_desc"] = [
                {"table_name": t} for t in case.target_table_names
            ]
        else:
            props["target_datasets"] = [
                {"target_dataset": {"schema": schema, "name": table}}
                for schema, table in zip(
                    case.target_resource_schemas, case.target_resource_tables
                )
            ]

    elif (
        case.operator
        == "ceh_core_idl.app.operators.services.get_max_loaded_version_operator"
    ):
        if case.source_resources:
            props[case.source_resource_field] = case.source_resources

    elif case.operator in IdlFlowResourceProcessorUnit._CF_CEH_TEMPLATE:
        if case.source_resources:
            props[case.source_resource_field] = case.source_resources
        if case.target_resources:
            props[case.target_resource_field] = case.target_resources

    yaml: Yaml = {
        "schema_version": 2.0,
        "flows": [
            {
                "id": "flow_test_task",
                "tasks": [
                    {
                        "id": f"task_{flow_id}",
                        case.operator_field: case.operator,
                        "properties": props,
                    }
                ],
            }
        ],
    }

    expected = build_flow_resources(
        resources=case.target_resources,
        resource_type=FlowResourceType.TARGET,
        algorithm=case.algorithm,
        flow_id=flow_id,
        schemas=case.target_resource_schemas,
        tables=case.target_resource_tables,
    ) + build_flow_resources(
        resources=case.source_resources,
        resource_type=FlowResourceType.SOURCE,
        algorithm=case.algorithm,
        flow_id=flow_id,
        schemas=case.source_resource_schemas,
        tables=case.source_resource_tables,
    )

    return yaml, expected


def build_flow_resources(
    resources: List[str],
    resource_type: FlowResourceType,
    algorithm: str,
    flow_id: int,
    schemas: List[Optional[str]],
    tables: List[Optional[str]],
) -> List[FlowResource]:
    if not resources:
        return []

    if len(resources) > 1:
        return [
            flow_resource(
                type=resource_type,
                algorithm=algorithm,
                flow_id=flow_id,
                resource_name=res,
                resource_table=flow_resource_table(
                    schema_name=None,
                    table_name=None,
                ),
            )
            for res in resources
        ]

    if schemas and tables and len(schemas) == len(tables):
        return [
            flow_resource(
                type=resource_type,
                algorithm=algorithm,
                flow_id=flow_id,
                resource_name=resources[0],
                resource_table=flow_resource_table(
                    schema_name=schema,
                    table_name=table,
                ),
            )
            for schema, table in zip(schemas, tables)
        ]

    return [
        flow_resource(
            type=resource_type,
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resources[0],
            resource_table=flow_resource_table(
                schema_name=schemas[0] if schemas else None,
                table_name=tables[0] if tables else None,
            ),
        )
    ]
