import uuid
from uuid import UUID

import pytest
from metaloader_rest_api.models import ResourceAction
from metaloader_rest_api.resource_repository import ResourceStageSequentialRepository
from sqlalchemy import text
from sqlalchemy.orm import Session


@pytest.fixture
def load_id() -> UUID:
    return uuid.uuid4()


@pytest.fixture
def stage_repo(db_session: Session, load_id: UUID) -> ResourceStageSequentialRepository:
    return ResourceStageSequentialRepository(session=db_session, load_id=load_id)


def test_create_table_includes_resource_type_column(
    stage_repo: ResourceStageSequentialRepository,
    db_session: Session,
    load_id: UUID,
):
    stage_repo.drop_table()
    stage_repo.create_table()

    # Test that we can insert with resource_type - this verifies the column exists
    stage_repo.add_resource_action(
        resource_cd="test_resource",
        action=ResourceAction.ADD,
        resource_type="ceh",
        definition={"resource_desc": "Test resource"}
    )

    # Verify the record was inserted with resource_type
    table_name = ResourceStageSequentialRepository.get_staging_table_name(load_id)
    result = db_session.execute(
        text(f"""
        SELECT resource_cd, data_action_type_rk, resource_type, definition
        FROM {table_name}
        WHERE resource_cd = 'test_resource'
        """)
    ).fetchone()

    assert result is not None
    assert result[2] == "ceh"  # resource_type column


def test_add_resource_action_with_resource_type(
    stage_repo: ResourceStageSequentialRepository,
    db_session: Session,
    load_id: UUID,
):
    stage_repo.drop_table()
    stage_repo.create_table()
    
    # Test ADD action with CEH resource type
    stage_repo.add_resource_action(
        resource_cd="ceh_resource",
        action=ResourceAction.ADD,
        resource_type="ceh",
        definition={"resource_desc": "CEH test resource"}
    )
    
    # Test MODIFY action with UNI resource type
    stage_repo.add_resource_action(
        resource_cd="uni_resource",
        action=ResourceAction.MODIFY,
        resource_type="uni",
        definition={"resource_desc": "UNI test resource"}
    )
    
    # Test DELETE action with resource type
    stage_repo.add_resource_action(
        resource_cd="delete_resource",
        action=ResourceAction.DELETE,
        resource_type="ceh"
    )
    
    # Verify all records were inserted correctly
    table_name = ResourceStageSequentialRepository.get_staging_table_name(load_id)
    results = db_session.execute(
        text(f"""
        SELECT resource_cd, data_action_type_rk, resource_type, definition
        FROM {table_name}
        ORDER BY resource_cd
        """)
    ).fetchall()
    
    assert len(results) == 3
    
    # Check CEH resource
    ceh_result = next(r for r in results if r[0] == "ceh_resource")
    assert ceh_result[1] == ResourceAction.ADD.value
    assert ceh_result[2] == "ceh"
    assert '"resource_desc": "CEH test resource"' in ceh_result[3]
    
    # Check UNI resource
    uni_result = next(r for r in results if r[0] == "uni_resource")
    assert uni_result[1] == ResourceAction.MODIFY.value
    assert uni_result[2] == "uni"
    assert '"resource_desc": "UNI test resource"' in uni_result[3]
    
    # Check DELETE resource
    delete_result = next(r for r in results if r[0] == "delete_resource")
    assert delete_result[1] == ResourceAction.DELETE.value
    assert delete_result[2] == "ceh"
    assert delete_result[3] is None


def test_add_resource_action_without_resource_type(
    stage_repo: ResourceStageSequentialRepository,
    db_session: Session,
    load_id: UUID,
):
    stage_repo.drop_table()
    stage_repo.create_table()
    
    # Test DELETE action without resource type (backward compatibility)
    stage_repo.add_resource_action(
        resource_cd="delete_resource_no_type",
        action=ResourceAction.DELETE
    )
    
    # Verify the record was inserted with NULL resource_type
    table_name = ResourceStageSequentialRepository.get_staging_table_name(load_id)
    result = db_session.execute(
        text(f"""
        SELECT resource_cd, data_action_type_rk, resource_type, definition
        FROM {table_name}
        WHERE resource_cd = 'delete_resource_no_type'
        """)
    ).fetchone()
    
    assert result is not None
    assert result[0] == "delete_resource_no_type"
    assert result[1] == ResourceAction.DELETE.value
    assert result[2] is None  # resource_type should be NULL
    assert result[3] is None  # definition should be NULL for DELETE


def test_add_resource_action_duplicate_detection(
    stage_repo: ResourceStageSequentialRepository,
    db_session: Session,
):
    stage_repo.drop_table()
    stage_repo.create_table()
    
    # Add first resource action
    existing_action = stage_repo.add_resource_action(
        resource_cd="duplicate_test",
        action=ResourceAction.ADD,
        resource_type="ceh",
        definition={"resource_desc": "First resource"}
    )
    assert existing_action is None
    
    # Try to add the same resource again - should return existing action
    existing_action = stage_repo.add_resource_action(
        resource_cd="duplicate_test",
        action=ResourceAction.MODIFY,
        resource_type="uni",
        definition={"resource_desc": "Second resource"}
    )
    assert existing_action == ResourceAction.ADD
